#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全测试 enhanced_recursive_split 函数的性能和内存问题
"""

import time
import signal
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.text_processing import enhanced_recursive_split


class TimeoutError(Exception):
    pass


def timeout_handler(signum, frame):
    raise TimeoutError("函数执行超时")


def test_with_timeout(test_func, timeout_seconds=10):
    """带超时的测试执行器"""
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(timeout_seconds)
    
    try:
        test_func()
        signal.alarm(0)  # 取消超时
        return True
    except TimeoutError:
        print(f"✗ 测试超时 ({timeout_seconds}秒)")
        return False
    except Exception as e:
        signal.alarm(0)
        print(f"✗ 发生错误: {e}")
        return False


def test_simple_case():
    """测试简单情况"""
    print("=== 测试简单情况 ===")
    
    text = "这是第一段。\n这是第二段。\n这是第三段。"
    
    start_time = time.time()
    result = enhanced_recursive_split(
        text,
        chunk_size=20,
        chunk_overlap=5
    )
    end_time = time.time()
    
    print(f"✓ 简单测试通过，耗时: {end_time - start_time:.3f}秒")
    print(f"  原文本长度: {len(text)}")
    print(f"  结果块数: {len(result)}")
    for i, chunk in enumerate(result):
        print(f"  块{i+1}: '{chunk}' (长度: {len(chunk)})")


def test_no_separators():
    """测试没有分隔符的情况"""
    print("\n=== 测试没有分隔符的情况 ===")
    
    def run_test():
        text = "a" * 1000  # 1000个字符，没有分隔符
        
        start_time = time.time()
        result = enhanced_recursive_split(
            text, 
            separators=["\n", ".", ","],  # 这些分隔符在文本中都不存在
            chunk_size=100,
            chunk_overlap=10
        )
        end_time = time.time()
        
        print(f"✓ 无分隔符测试通过，耗时: {end_time - start_time:.3f}秒")
        print(f"  结果块数: {len(result)}")
        print(f"  第一个块长度: {len(result[0]) if result else 0}")
        print(f"  最后一个块长度: {len(result[-1]) if result else 0}")
    
    success = test_with_timeout(run_test, 5)
    if not success:
        print("  这个测试可能触发了性能问题")


def test_repeated_characters():
    """测试重复字符的情况"""
    print("\n=== 测试重复字符情况 ===")
    
    def run_test():
        text = "aaaaaaaaaa" * 100  # 1000个重复字符
        
        start_time = time.time()
        result = enhanced_recursive_split(
            text,
            chunk_size=50,
            chunk_overlap=25
        )
        end_time = time.time()
        
        print(f"✓ 重复字符测试通过，耗时: {end_time - start_time:.3f}秒")
        print(f"  结果块数: {len(result)}")
        
        # 检查重叠是否正确
        if len(result) > 1:
            overlap_found = False
            for i in range(len(result) - 1):
                current_end = result[i][-10:]  # 取最后10个字符
                next_start = result[i+1][:10]  # 取前10个字符
                if current_end in result[i+1]:
                    overlap_found = True
                    break
            print(f"  发现重叠: {overlap_found}")
    
    success = test_with_timeout(run_test, 5)
    if not success:
        print("  这个测试可能触发了重叠检测的性能问题")


def test_chunk_min_with_duplicates():
    """测试chunk_min参数与重复内容的组合"""
    print("\n=== 测试chunk_min与重复内容 ===")
    
    def run_test():
        # 创建包含重复内容的文本
        base_text = "重复内容"
        text = base_text + "。" + base_text + "。" + base_text
        
        start_time = time.time()
        result = enhanced_recursive_split(
            text,
            chunk_size=20,
            chunk_overlap=5,
            chunk_min=10
        )
        end_time = time.time()
        
        print(f"✓ chunk_min测试通过，耗时: {end_time - start_time:.3f}秒")
        print(f"  原文本: '{text}'")
        print(f"  结果块数: {len(result)}")
        for i, chunk in enumerate(result):
            print(f"  块{i+1}: '{chunk}' (长度: {len(chunk)})")
    
    success = test_with_timeout(run_test, 5)
    if not success:
        print("  这个测试可能触发了重复检测的性能问题")


def test_many_separators():
    """测试大量分隔符的情况"""
    print("\n=== 测试大量分隔符情况 ===")
    
    def run_test():
        # 创建包含大量分隔符的文本
        text = "。" * 500 + "文本" + "。" * 500
        
        start_time = time.time()
        result = enhanced_recursive_split(
            text,
            separators=["。", "，", "\n"],
            chunk_size=100,
            chunk_overlap=10
        )
        end_time = time.time()
        
        print(f"✓ 大量分隔符测试通过，耗时: {end_time - start_time:.3f}秒")
        print(f"  结果块数: {len(result)}")
        print(f"  非空块数: {len([c for c in result if c.strip()])}")
    
    success = test_with_timeout(run_test, 5)
    if not success:
        print("  这个测试可能触发了分隔符处理的性能问题")


if __name__ == "__main__":
    print("开始安全测试 enhanced_recursive_split 函数...")
    
    test_simple_case()
    test_no_separators()
    test_repeated_characters()
    test_chunk_min_with_duplicates()
    test_many_separators()
    
    print("\n测试完成！")
    print("\n=== 问题总结 ===")
    print("1. 函数在处理没有分隔符的长文本时可能会超时")
    print("2. 重复字符的重叠检测可能导致性能问题")
    print("3. chunk_min参数的重复内容检测算法效率低下")
    print("4. 大量分隔符可能导致递归深度过深")
