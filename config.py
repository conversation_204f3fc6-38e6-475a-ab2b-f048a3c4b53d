# from rainbow_sdk.rainbow_client import RainbowClient
import logging
from ssv_logger import ssv_logger
ssv_logger('config.log')
logger = logging.getLogger(__name__)

LOG_MODEL_CALL_KEYS=''
GATE_URL_REMOTE= 'http://***************'
GATE_URL_LOCAL = 'http://tanlive-rag-apis'
GATE_URL_OLLAMA='http://***************:11434'
# chat、gpt3、t2vec、search、finetune-serv ip地址
CHAT_URL = f"{GATE_URL_LOCAL}/chat"
GPT3_URL = f"{GATE_URL_LOCAL}/gpt3"
T2VEC_URL = f"{GATE_URL_REMOTE}/t2vec"
SEARCH_URL = f"{GATE_URL_REMOTE}/search"
TEXTMOD_URL = f"{GATE_URL_REMOTE}/tencent/textmod"
GET_CHUNKS_URL = f"{GATE_URL_REMOTE}/get_split_chunks"

RERANK_URL = None # f"{GATE_URL_REMOTE}/rerank"
RERANK_MDL = 'qwen3-0.6b'
RERANK_BATCH_SIZE = 8
RERANK_THRESHOLD = 0.5

RECURSIVE_SPLIT_SEPARATORS = ['##', '\n\n', '\n', '。', '. ']

# CHUNK_SEG_FORMAT = [INH:parent_doc_id]
CHUNK_SEG_THRESHOLD = 0.6
CHUNK_SEG_EMB_MODEL = 'zh'
CHUNK_SEG_SPLITTERS = ['. ', '。', '\n']

# MAX_TOKEN_LEN = 4096 # split chunk max size
V_BATCHSIZE = 200


HY_KEYS = {}

R_MODELS = {
    'emb_zh':{'url':GATE_URL_REMOTE,'path':'t2vec_zh'},
    'emb_en':{'url':GATE_URL_REMOTE,'path':'t2vec_en'},
    'emb_bge-m3':{'url':GATE_URL_REMOTE,'path':'api/embed'}
    }

SQL_HOST='********'
SQL_DB='db_tanlive_rag'
SQL_PROGRESS_TABLE='rag_progress'
SQL_WEBCACHE_TABLE=''
SQL_U='root'
SQL_PW='PwMc3FF7JBX$'

OPENAI_MODELS = {
    # 'gemini-2.0-flash':{
    #     'url':'https://generativelanguage.googleapis.com/v1beta/openai/',
    #     'key':'',
    # },
    'gemma3:27b':{
        'url':f'{GATE_URL_REMOTE}/v1',
        'key':'ollama',
    },
    'deepseek-r1':{
        'url':'https://api.lkeap.cloud.tencent.com/v1',
        'key':'sk-BYHDKykUZEdYjVdF2hacx2Wi13YNth2sj9TM2HxCZxJMOfzy',
    },
    'hunyuan-standard':{
        'url':'https://api.hunyuan.cloud.tencent.com/v1',
        'key':'sk-PTAtf3dvQNJiYUTrYlvljafkzDiGDYngVQptvTAo4emIgQAo',
    },
    'hunyuan-vision':{
        'url':f'{GATE_URL_REMOTE}/tencent/llm/hunyuan_stream/',
        'key':'sk-tanlive',
    },
    'hunyuan-standard-ssv':{
        'url':f'{GATE_URL_REMOTE}/tencent/llm/hunyuan_stream/',
        'key':'sk-tanlive',
    },
    'hunyuan':{
        'url':f'{GATE_URL_REMOTE}/tencent/llm/hunyuan_stream/',
        'key':'sk-tanlive',
    },
    'hunyuan-lite':{
        'url':f'{GATE_URL_REMOTE}/tencent/llm/hunyuan_stream/',
        'key':'sk-tanlive',
    },
}

REMOTE_MODELS = {
    'deepseek-v3-0324':{
        'url':f'{GATE_URL_REMOTE}/tencent/llm/venus',
        'model_name':'deepseek-v3-local',
        'key':'',
    },
    # 'hunyuan-lite':{
    #     'url':f'{GATE_URL_REMOTE}/tencent/llm/hunyuan',
    #     'model_name':'hunyuan-lite',
    #     'key':'',
    # },
    # 'hunyuan':{
    #     'url':f'{GATE_URL_REMOTE}/tencent/llm/hunyuan',
    #     'model_name':'hunyuan',
    #     'key':'',
    # },
    # 'gpt4o':{
    #     'url':f'{GATE_URL_REMOTE}/tencent/llm/gpt4o',
    #     'model_name':'gpt4o',
    #     'key':'',
    # },
    # 'gemini-2.0-flash':{
    #     'url':f'{GATE_URL_REMOTE}/tencent/llm/gemini',
    #     'model_name':'gemini-2.0-flash',
    #     'key':'',
    # },
}
FALLBACK_ENGINES = ""

ES_CONFIGS = {
    "ldxkujv6" : {"ip": "*********", "port": "9200", "user": "elastic", "password": "BKuKbQ5Tc*Sr"},
    "b2x0ejlp" : {"ip": "***********", "port": "9200", "user": "elastic", "password": "v59XFezw^1b"},
}

# 新增：多环境模型配置，支持负载均衡
# 多环境模型配置，支持负载均衡
MULTI_ENV_MODELS = {
    'gemini-2.0-flash':{
        'openai': {
            'priority': 1,
            'url':'https://generativelanguage.googleapis.com/v1beta/openai/',
            'key':'AIzaSyCvda0ILMmvfNdwFBOQt2l9N_C3oQHIoRM',
        }
    },
    'gemini-2.5-pro-preview-06-05':{
        'openai': {
            'priority': 1,
            'url':'https://generativelanguage.googleapis.com/v1beta/openai/',
            'key':'AIzaSyCvda0ILMmvfNdwFBOQt2l9N_C3oQHIoRM',
        }
    },
    'hunyuan-standard': {
        'openai': {
            'priority': 2,
            'url': 'https://api.hunyuan.cloud.tencent.com/v1',
            'key': 'sk-PTAtf3dvQNJiYUTrYlvljafkzDiGDYngVQptvTAo4emIgQAo'
        },
        'remote': {
            'concurrency_limit': 33,
            'priority': 1,
            'url':f'{GATE_URL_REMOTE}/tencent/llm/hunyuan_stream/',
            'model_name': 'hunyuan',
            'key': 'sk-tanlive'
        }
    },
    'hunyuan-vision': {
        'openai': {
            'concurrency_limit': 5,
            'priority': 2,
            'url': 'https://api.hunyuan.cloud.tencent.com/v1',
            'key': 'sk-PTAtf3dvQNJiYUTrYlvljafkzDiGDYngVQptvTAo4emIgQAo'
        },
        'remote': {
            'concurrency_limit': 5,
            'priority': 1,
            'url':f'{GATE_URL_REMOTE}/tencent/llm/hunyuan_stream/',
            'model_name': 'hunyuan',
            'key': 'sk-tanlive'
        }
    },
    'hunyuan-lite': {
        'openai': {
            'concurrency_limit': 5,
            'priority': 2,
            'url': 'https://api.hunyuan.cloud.tencent.com/v1',
            'key': 'sk-PTAtf3dvQNJiYUTrYlvljafkzDiGDYngVQptvTAo4emIgQAo'
        },
        'remote': {
            'concurrency_limit': 12,
            'priority': 1,
            'url':f'{GATE_URL_REMOTE}/tencent/llm/hunyuan_stream/',
            'model_name': 'hunyuan',
            'key': 'sk-tanlive'
        }
    },
    'hunyuan': {
        'openai': {
            'concurrency_limit': 5,
            'priority': 2,
            'url': 'https://api.hunyuan.cloud.tencent.com/v1',
            'key': 'sk-PTAtf3dvQNJiYUTrYlvljafkzDiGDYngVQptvTAo4emIgQAo'
        },
        'remote': {
            'concurrency_limit': 5,
            'priority': 1,
            'url':f'{GATE_URL_REMOTE}/tencent/llm/hunyuan_stream/',
            'model_name': 'hunyuan',
            'key': 'sk-tanlive'
        }
    },
    'hunyuan-turbos-vision': {
        'openai': {
            'concurrency_limit': 5,
            'priority': 2,
            'url': 'https://api.hunyuan.cloud.tencent.com/v1',
            'key': 'sk-PTAtf3dvQNJiYUTrYlvljafkzDiGDYngVQptvTAo4emIgQAo'
        },
        'remote': {
            'concurrency_limit': 10,
            'priority': 1,
            'url':f'{GATE_URL_REMOTE}/tencent/llm/hunyuan_stream/',
            'model_name': 'hunyuan',
            'key': 'sk-tanlive'
        }
    },
    'deepseek-r1': {
        'openai': {
            'priority': 1,
            'url': 'https://api.lkeap.cloud.tencent.com/v1',
            'key': 'sk-BYHDKykUZEdYjVdF2hacx2Wi13YNth2sj9TM2HxCZxJMOfzy'
        }
    },
    'deepseek-v3': {
        'openai': {
            'priority': 1,
            'url': 'https://api.lkeap.cloud.tencent.com/v1',
            'key': 'sk-BYHDKykUZEdYjVdF2hacx2Wi13YNth2sj9TM2HxCZxJMOfzy'
        }
    },
    'gemma3:27b': {
        'openai': {
            'priority': 1,
            'url': f'{GATE_URL_OLLAMA}/v1',
            'key': 'ollama'
        }
    }
}

# 全局负载均衡配置
LOAD_BALANCE_CONFIG = {
    'enable_auto_fallback': True,  # 是否启用自动降级
    'fallback_timeout': 5,  # 降级超时时间（秒）
}

# Redis配置
REDIS_CONFIG = {
    'url': 'redis://10.0.0.4:6379',  # Redis连接URL
    'key_prefix': 'load_balancer',    # Redis键前缀
    'password': 'JWEtXqjMjFUK',      
}

DEFAULT_CONCURRENCY_LIMIT = 1000

# 需要将image_url转换为base64的模型列表
IMAGE_URL_BASE64_MODELS = [
    'gemini-2.0-flash',
    'gemini-2.5-pro-preview-06-05'
]

# 支持reasoning_effort参数的模型列表
REASONING_EFFORT_MODELS = ['gemini-2.5-pro-preview-06-05']
