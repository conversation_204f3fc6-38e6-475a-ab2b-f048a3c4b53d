#encoding=utf8
import json
import logging
import random
import time
import os

from concurrent.futures import Thr<PERSON>PoolExecutor
from ssv_logger import ssv_logger
ssv_logger('llms.log')
logger = logging.getLogger(__name__)

import numpy as np
# import tiktoken
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
import httpx
import asyncio
import traceback
from openai import AsyncOpenAI,OpenAI
from typing import Optional


from utils.query_parser import parse_fastapi_data
from utils.model_logger import log_model_call_async, model_logger
import base64

from load_balancer import load_balancer
from config import REASONING_EFFORT_MODELS


# app = Flask(__name__)
gpt_app = FastAPI(debug=True,docs_url=None,redoc_url=None)
    
@log_model_call_async(key='request_openai')
async def request_openai(completion_params: dict, model_config: dict[str, str], timeout: Optional[int] = 30):
    if not model_config:
        raise HTTPException(status_code=500, detail='model not in openai list')
    key = model_config.get('key')
    ep =  model_config.get('url')
    
    client_kwargs = {
        'api_key': key,
        'base_url': ep,
        'max_retries': 0,
    }
    if timeout is not None:
        client_kwargs['timeout'] = timeout
    
    # 初始化OpenAI客户端
    client = AsyncOpenAI(**client_kwargs)
    ret = await client.chat.completions.create(**completion_params)
    return ret

async def openai_param(llm_kwargs: dict):
    model = llm_kwargs.get('model')
    completion_params = {
        "model": model,
        "messages": llm_kwargs['messages'],
    }
    if 'stream' in llm_kwargs:
        completion_params["stream"] = llm_kwargs['stream']
    else:
        completion_params["stream"] = False 
    
    if 'reasoning_effort' in llm_kwargs:
        completion_params["reasoning_effort"] = llm_kwargs.get('reasoning_effort', 'medium')
    if 'extra_body' in llm_kwargs:
        completion_params['extra_body'] = {'extra_body': llm_kwargs.get('extra_body')} 
    if 'messages' not in llm_kwargs:
        if 'text' in llm_kwargs:
            completion_params['messages'] = [{'role': 'user', 'content': llm_kwargs['text']}]
    return completion_params

@log_model_call_async(key='request_openai_stream')
async def request_openai_stream(completion_params: dict, model_config: dict[str, str], timeout: Optional[int] = 60):
    if not model_config:
        raise HTTPException(status_code=500, detail='model not in openai list')
    key = model_config.get('key')
    ep = model_config.get('url')
    
    client_kwargs = {
        'api_key': key,
        'base_url': ep,
        'max_retries': 0,
    }
    if timeout is not None:
        client_kwargs['timeout'] = timeout
    
    # 初始化OpenAI客户端
    client = OpenAI(**client_kwargs)
    stream = client.chat.completions.create(**completion_params)
    async def event_generator():
        for chunk in stream:
            # 让出控制权给其他协程
            await asyncio.sleep(0)
            
            # if not getattr(chunk, 'choices', None) and not getattr(
            #             chunk, 'reasoning_content', None):
            #     continue
            # print('===>',chunk.model_dump())
            yield f"data: {json.dumps(chunk.model_dump())}\n\n"

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )

async def request_remote(llm_kwargs, url):
    model = llm_kwargs.get('model')
    sid = llm_kwargs.get('session_id', '')
    logger.info(f"[{sid}] remote request to {url} with parameters: {llm_kwargs}")
    if 'messages' not in llm_kwargs:
        if 'text' in llm_kwargs:
            llm_kwargs['messages'] = [{'role': 'user', 'content': llm_kwargs['text']}]
        else:
            raise HTTPException(status_code=500, detail='should provide text or messages')
    try:
        async with httpx.AsyncClient() as client:
            ret = await client.post(url,json=llm_kwargs)
            return httpx.Response(status_code=ret.status_code,
                                  headers=ret.headers,
                                  content=ret.content)
    except Exception as e:
        return {'code': 90020, 'info': f'request remote {model} err: {e}'}
    
async def a_make_request(url, headers, data):
    async with httpx.AsyncClient() as client:
        async with client.stream('POST', url, headers=headers, json=data, timeout=600) as response:
            if response.status_code != 200:
                logger.error(f'make_request failed: {response.status_code},{response.text}')
                raise ValueError(f"API returned an error,{response.status_code}")
            
            # 逐行读取并立即yield
            buffer = b""
            async for chunk in response.aiter_bytes():
                buffer += chunk
                while b'\n' in buffer:
                    line, buffer = buffer.split(b'\n', 1)
                    if line:
                        # print('===line',line,time.time())
                        yield line + b'\n\n'
            # 处理最后可能剩余的数据
            if buffer:
                yield buffer
    
async def request_remote_stream(llm_kwargs: dict, url):
    model = llm_kwargs.get('model')
    sid = llm_kwargs.get('session_id', '')
    headers = {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
        "X-Accel-Buffering": "no"
    }
    t0 = time.time()
    try:
        if 'image' in llm_kwargs['model']:
            llm_kwargs['prompt'] = llm_kwargs['text']
            generator = a_make_request(url,
                                headers, llm_kwargs)
        else:
            generator = a_make_request(url,
                                headers, llm_kwargs)
            logger.info(f'[{sid}] {model} return; cost of time: {time.time() - t0}')

        async def stream_generator():
            async for chunk in generator:
                yield chunk

        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f'request remote err: {e}')
    
@retry(
    stop=stop_after_attempt(3),
    wait=wait_fixed(0.2),
    retry=retry_if_exception_type((Exception)),
    reraise=True
)
async def request_stream_with_load_balance(llm_kwargs: dict, timeout: Optional[int] = None):
    model = llm_kwargs.get('model')
    sid = llm_kwargs.get('session_id', '')
    endpoint_info = await load_balancer.get_best_endpoint(model)
    if endpoint_info:
        env_type, config = endpoint_info
        try:
            logger.info(f"[{sid}] stream_with_load_balance using load balanced endpoint: {model}:{env_type}")
            param = await openai_param(llm_kwargs)
            config['session_id'] = sid
            response = await request_openai_stream(param, config, timeout)
            return response
        except Exception as e:
            logger.error(f"[{sid}] stream_with_load_balance error with endpoint {model}:{env_type}: {e}")
            # 标记端点为不健康
            load_balancer.mark_endpoint_unhealthy(model, env_type)
            raise
        finally:
            # 释放端点
            load_balancer.release_endpoint(model, env_type)
    else:
        logger.error(f'[{sid}]request_stream_with_load_balance catch {model} err: {traceback.format_exc()}')
        raise HTTPException(status_code=500, detail='model not available')
    
@retry(
    stop=stop_after_attempt(3),
    wait=wait_fixed(0.2),
    retry=retry_if_exception_type((Exception)),
    reraise=True
)
async def request_with_load_balance(llm_kwargs: dict, timeout: Optional[int] = None):
    model = llm_kwargs.get('model')
    sid = llm_kwargs.get('session_id', '')
    endpoint_info = await load_balancer.get_best_endpoint(model)
    if endpoint_info:
        env_type, config = endpoint_info
        try:
            logger.info(f"[{sid}] request_with_load_balance using load balanced endpoint: {model}:{env_type}")
            param = await openai_param(llm_kwargs)
            config['session_id'] = sid
            response = await request_openai(param, config, timeout)
            logger.info(f"[{sid}] request_with_load_balance response from {model}:{env_type}: {str(response)[:1000]}...")
            return response
        except Exception as e:
            logger.error(f"[{sid}] request_with_load_balance request error with endpoint {model}:{env_type}: {e}")
            # 标记端点为不健康
            load_balancer.mark_endpoint_unhealthy(model, env_type)
            raise
        finally:
            # 释放端点
            load_balancer.release_endpoint(model, env_type)
    else:
        logger.error(f'[{sid}]request_with_load_balance catch {model} err: {traceback.format_exc()}')
        raise HTTPException(status_code=500, detail='model not available')
    

if __name__ == '__main__':
    # r=asyncio.run(request_remote({'model': 'deepseek-v3-0324',
    #                              'messages': [{'role': 'user', 'content': '你好'}]}))
    # r=asyncio.run(request_openai_stream({'model': 'gemma3:27b','text':'你好','stream':False}))
    print(r.json())


