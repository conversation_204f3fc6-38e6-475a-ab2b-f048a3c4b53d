

# 图文混排的实现示例

## 配置
- **进入知识库的文档**: https://casisd.cas.cn/ttxw1/zlyjytt/202405/P020240529314820155762.pdf
- **使用的collection**: `roizhao-test_create_zh` (`/collection/create`直接入库文本内容)

## 图片相关的预处理：
    1. 使用 `utils.text_loader.parse_pdf_images` 方法提取文档中图片caption为如下文件：
        - 无线通信词云图2012～2017.png
        - 本章基于科学结构图谱进行科学结构及其演变的分析。研究领域的构成及研究领域间的关.png
        - 本章对研究领域进一步分析，分析研究领域的学科交叉性、识别新兴热点研究领域，以及.png
        - 本章将科学资助和科学产出进行关联分析。通过研究科学基金对SCI 论文资助情.png
        - 本章选取中国及其他发达国家和发展中国家中有代表性的11 个国家（包括美国、英国、.png
        - 机器学习词云图2012～2017.png
        - 气候变化词云图2012～2017.png
        - 环境治理词云图2012～2017.png
        - 研究领域的学科交叉性、新颖性以及对技术创新的影响.png
        - "科学结构图谱"的主体分析单元是热点"研究领域"，它通过对高被引论文的同被引关系.png
        - 科技创新已成为推动经济社会发展的主要力量，新一轮科技革命和产业变革的重大历史机.png
        - 第一章.png
        - 第四章.png
        - 肠道微生物与健康词云图2012～2017.png
        - 肿瘤免疫治疗词云图2012～2017.png
        - 表观遗传学词云图2012～2017.png
        - 量子物理词云图2012～2017.png
        - 钙钛矿材料与器件词云图2012～2017.png
        - ...

    > **注意**: 这里只是示例，实际操作时只要能提取图片和caption就行

    2. 图片进入cos（demo中跳过）
    3. 用 `/collection/edit` 方法将图片caption内容嵌入知识库

    **示例代码**:
    ```python
    for filename in os.listdir('img_dir'):
        if len(l) > 6:
            l = l.replace("'", '').replace('\b', '').replace('$', '')
            r = requests.post(f'http://{URL_TEST}/edit_collection', json={
                "collection_name": "roizhao-test_create_zh",
                "text": f"[IMG:{l}]",
                "threshold": 0.98,
                "lang": 'zh'
            })
    ```

## 使用

    ### 在 `/chat` 接口中添加图文混排占位指令

    在 `prompt_prefix` 中加入图文混排占位的指令：

    ```python
    r = requests.post('https://ml-serv.ssv.qq.com/chat', json={
        "collection_name": "roizhao-test_create_zh",
        "text": "肠道微生物与健康领域的研究多吗？",
        "model": "hunyuan",
        'judge_prompt_type': False,
        "threshold": 0.55,
        "top-n": 10,
        "security": False,
        "prompt_prefix": """你是智能助手，名字是小海豹。
        请从后面的参考资料提取相关内容用第一人称回答问题：{}
        回答时务必遵循以下要求：
        1. 不要说你是谁
        2. (每段回答的最后用[n]表明这段回答参考的是第几个材料，n对应材料的顺序，不要出现未转义的`[n]`本身)
        3. 形如[IMG:图片描述.png]格式的参考内容为图片的描述，在回答中必须原样保留（即保持[IMG:图片描述.png]格式），作为图文混排的占位符
        相关资料：
        """,
        "lang": "zh"
    })
    ```

    **返回示例**：
    ```json
    {
        "created": 1751425691,
        "id": "ea0bbf182ac341e392de09a19b8adef9",
        "object": "chat.completion",
        "model": "hunyuan",
        "version": "202502271540",
        "search_info": {"mindmap": {}},
        "processes": {},
        "customized_info": "医疗",
        "usage": {
            "prompt_tokens": 765,
            "completion_tokens": 181,
            "total_tokens": 946
        },
        "ref": "科学结构2021.pdf\n22d94ecc-3f17-4119-b15b-f56429a43ccb.txt\n22d94ecc-3f17-4119-b15b-f56429a43ccb.txt\n科学结构2021.pdf\n科学结构2021.pdf",
        "references": [
            "科学结构2021.pdf",
            "22d94ecc-3f17-4119-b15b-f56429a43ccb.txt",
            "22d94ecc-3f17-4119-b15b-f56429a43ccb.txt",
            "科学结构2021.pdf",
            "科学结构2021.pdf"
        ],
        "enhancement": "doc",
        "data": {
            "final_query": "肠道微生物与健康领域的研究多吗？",
            "response": {"role": "assistant"},
            "gpt": "肠道微生物与健康领域的研究非常多，从文章数量上来看已经翻了一番，研究前沿数也有明显增加。[1]\n这个领域的研究工作不断深入，形成了多样且更为细致的研究领域，研究范围从最初的2个扩展到目前的11个领域。[5]\n肠道菌群一直是该领域的核心研究热点，最初与炎症性肠病相关，后来逐渐扩展到消化系统疾病等多个方向。[1]\n此外，研究人员的研究方向也在不断转变，从早期的乳糜泻、肠黏膜研究，到现在关注健康微生物群、婴儿抗生素暴露等问题，说明该领域的关注点越来越广泛。[4]\n[IMG: 肠道微生物与健康词云图2012～2017.png][IMG:40 肠道微生物与健康词云图2010～2015.png]",
            "judge": "Pass",
            "level": 3,
            "score": 0,
            "label": "",
            "session_id": "f8f50701-86d3-4d52-ac83-94f8aa235425"
        },
        "code": 10000,
        "info": ""
    }
    ```

    ### 返回给用户时替换占位符

    在返回给用户时，用 COS 图片 URI 替换对应的占位符。

