#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简单的无限循环复现测试
"""

import sys
import os
import signal
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.text_processing import enhanced_recursive_split


def timeout_handler(signum, frame):
    raise Exception("超时：证实了无限循环")


def test_minimal():
    """最小测试用例：仅用6个字符就能触发无限循环"""
    
    print("=== 最小无限循环测试 ===")
    print("仅需6个相同字符即可触发第129-131行的无限循环")
    print()
    
    # 最小测试用例
    text = "aaaaaa"  # 仅6个字符
    chunk_size = 4
    chunk_overlap = 2
    
    print(f"测试文本: '{text}' (长度: {len(text)})")
    print(f"参数: chunk_size={chunk_size}, chunk_overlap={chunk_overlap}")
    print()
    
    # 设置2秒超时
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(2)
    
    try:
        result = enhanced_recursive_split(
            text,
            separators=["\n"],  # 文本中不存在的分隔符
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        signal.alarm(0)
        print("✗ 意外：函数正常完成了")
        print(f"结果: {result}")
        
    except Exception as e:
        signal.alarm(0)
        if "超时" in str(e):
            print("✓ 成功复现无限循环！")
            print("原因分析：")
            print("1. 文本 'aaaaaa' 中没有分隔符 '\\n'")
            print("2. 进入固定长度切分逻辑")
            print("3. 第一次切分: start=0, end=4, 得到 'aaaa'")
            print("4. 第二次切分准备: start=4-2=2, end=min(2+4,6)=6")
            print("5. 检查重复: chunk[start-1]=chunk[1]='a', chunk[end-1]=chunk[5]='a'")
            print("6. 由于 'a' == 'a'，进入while循环")
            print("7. start和end不断减1，但字符始终相等，形成无限循环")
        else:
            print(f"其他错误: {e}")


if __name__ == "__main__":
    test_minimal()
