
from pprint import pprint
import httpx
import pathlib
import os
import json
import time
URL_DEV='9.134.7.49:8001'
URL_UAT=''
URL_TEST='180.153.202.187'
URL_GATE='101.226.141.241'
URL_NGINX=''
ES_URL_DEV='21.47.100.249'


def test_text_mod(text='如何购买摇头丸'):
    r=httpx.post(f'http://{URL_GATE}/tencent/textmod',
        json={'text':text},timeout=20)
    try:
        r = r.json()
        pprint(r)
    except Exception as e:
        print(e)
        print(r.text)
    return r


def test_chat_text(text='如何购买摇头丸'):
    r=httpx.post(f'http://{URL_DEV}/v1/chat',
            json={'text':text,
                'model':'hunyuan-standard',
                'session_id':'7740ae56-f4a8-41d8-85b2-27503ace2983'},
            timeout=20)
    try:
        r = r.json()
        # 应该有data.gpt字段，str类型，长度大于0
        assert isinstance(r['data']['gpt'],str)
        assert len(r['data']['gpt']) > 0
        pprint(r)
    except Exception as e:
        print(e)
        print(r.text)
    return r


def test_chat_messages(messages=[
        {'role':'user','content':'孩子吃什么营养价值高？'},
        {'role':'assistant','content':'作为一只小海豹，我虽然生活在海洋环境中，但我也能理解人类对于孩子饮食的关注。根据你提供的资料，对于孩子来说，营养价值高的食物应该包括植物油、有机水果和蔬菜。这些食物不仅能为孩子提供必要的营养，还有助于他们的身体发育。同时，要注意避免给孩子食用胆固醇过高的油类，以免刺激他们的肠胃，影响消化功能。总的来说，为孩子提供均衡、多样的饮食是非常重要的。'},
        {'role':'user','content':'那么哪些价值低'}
    ]):
    r=httpx.post(f'http://{URL_DEV}/v1/chat',json={
        #"collection_name": "roizhao-test_create_zh",
        "messages": messages,
        "model": "hunyuan-lite",
        "judge_prompt_type":True,
        "security": False,
        "search": True,
        "search_rewrite":True,
        "threshold":0.7,
        "top_n":2,
        "prompt_prefix":"我是一只小海豹，从后面的资料中提取相关内容用第一人称回答问题：{}，\n 以下是你自己在网络上检索到的资料：\n"
    },timeout=50)
    try:
        r = r.json()
        # 应该有data.gpt字段，str类型，长度大于0
        assert isinstance(r['data']['gpt'],str)
        assert len(r['data']['gpt']) > 0
        pprint(r)
    except Exception as e:
        print(e)
        print(r.text)
    return r



async def test_chat_stream_httpx(messages=[
        {'role':'user','content':'hello'},
    ]):
    t0=time.time()
    async with httpx.AsyncClient() as client:
        async with client.stream(
            'POST',
            f'http://{URL_DEV}/v1/chat_stream',
            json={
                "model": "hunyuan-standard-ssv",
                "messages": messages,
                "stream": True
            },
            headers={
                "Content-Type": "application/json",
                "Cache-Control": "no-cache",
                "X-Accel-Buffering": "no"
            },
            timeout=60.0
        ) as response:
            async for line in response.aiter_lines():
                if line:
                    print('==>',line,time.time()-t0)  # 可选：打印时间戳来验证流式输出


async def test_chat_stream_openai(messages=[
        {'role':'user','content':'hello'},
    ]):
    from openai import OpenAI
    client = OpenAI(api_key='sk-xxxxx',
                    base_url='http://127.0.0.1:8001/v1')
    response = client.chat.completions.create(
        model="hunyuan-lite",
        messages=messages,
        stream=True
    )
    t0=time.time()
    async for chunk in response:
        print('==>',chunk,type(chunk),time.time()-t0)



async def test_chat_stream_recall(text='你们有没有推荐的小小的洗碗机，里面没有塑料的啊，我看了市面上大部分底部那块都是塑料。'):
    t0=time.time()
    async with httpx.AsyncClient() as client:
        async with client.stream(
            'POST',
            f'http://{URL_TEST}/chat_stream',
            json={
                "text": text,
                "model": "gemma",
                "security": False,
                "search": True,
                "prompt_prefix": "请回答用户的问题：{}，开头不要说“根据”二字 \n 以下是你找到的相关知识：\n",
                "stream": True,
                "lang": "zh",
                "threshold": 0.3,
                "top_n": 5
            },
            headers={
                "Content-Type": "application/json",
                "Cache-Control": "no-cache",
                "X-Accel-Buffering": "no"
            },
            timeout=60.0
        ) as response:
            async for line in response.aiter_lines():
                if line:
                    print(line)
                    print('===', time.time()-t0) 


def test_search(text='农机补贴'):
    r=httpx.post(f'http://{URL_DEV}/search',json={
        "text": text,
        "engine": "wx",
        "top_n": 2
    })
    try:
        r = r.json()
        pprint(r)
    except Exception as e:
        print(e)
        print(r.text)
    return r


def test_get_split_chunks(file_path='/data/dat/rag/eb0ea9e8-1283-4d04-96f7-5709d2678bf2.txt'):
    r=httpx.post(f'http://{URL_DEV}/collection/get_split_chunks',
        data={'collection_name':'roizhao-test_create_zh',
            'chunk_size':300,
            'chunk_overlap':200,
            'chunk_min':100
        },
        files=[('files',open(file_path,'rb'))]
    )
    try:
        r = r.json()
        pprint(r)
    except Exception as e:
        print(e)
        print(r.text)
    return r


def test_create_collection(file_path='/data/dat/rag/科学结构2021.pdf'):
    r=httpx.post(f'http://{URL_DEV}/v1/collection/create',
        data={
            'collection_name': 'roizhao-test_create_img_zh',
            'return_ids': True
        },
        files=[('files',open(file_path,'rb'))],
        timeout=120
    )
    try:
        r = r.json()
        pprint(r)
    except Exception as e:
        print(e)
        print(r.text)
    return r

def test_edit_collection(file_path='/data/dat/rag/tmp'): # 图片描述:地址
    for file in os.listdir(file_path):
        if file.endswith('.png'):
            index_content = file.split('.')[0]
            content = f'[IMG:{file}]'
            r=httpx.post(f'http://{URL_DEV}/v1/collection/edit',
                json={
                    "collection_name": "roizhao-test_create_img_zh",
                    "index_content": index_content,
                    "content": content
                },
                timeout=120
            )
            try:
                r = r.json()
                pprint(r)
            except Exception as e:
                print(e)
                print(r.text)
            return r

def test_search_collection_oneshot(text='低碳出行的方式有哪些啊哦吗'):
    r=httpx.post(f'http://{URL_DEV}/v1/search_collection_oneshot',
        json={
            "collection_name": "roizhao-test_create_zh",
            "text": text,
            "model": "hunyuan-standard",
            "threshold": 0.2,
            "security": False,
            "prompt_prefix": "你的角色是智能回答助手，名字是小海豹。\\n你必须遵循以下要求：不要说你是谁。从后面的资料中提取相关内容用第一人称回答问题：{}，\\n 以下是你自己在网络上检索到的资料：\\n(每段回答的最后用[n]表明这段回答参考的是第几个材料，n对应材料的顺序，不要出现[n])",
            "agent_id": "climate_tech_tanlive",
            "lang": "zh",
            "text_recall_top_n": 1,
            "top_n": 2
        },
        timeout=120
    )
    try:
        r = r.json()
        pprint(r)
    except Exception as e:
        print(e)
        print(r.text)
    return r


def test_agent_hierarchy_async(file_path='/Users/<USER>/Documents/carbon_test/long317051.txt'):
    with open(file_path,'r',encoding='utf-8') as f:
        t=f.read()
    r=httpx.post(f'http://{URL_DEV}/agent/text/hierarchy-async',data={'text': t[:10000],
                    'agent_id':'climate_tech_tanlive',
                    'model':'sft','chunk_size':2000,'prompt_init_prefix':'摘要以下内容'},
                       timeout=1000)
    if r.status_code == 200:
        for line in r.iter_lines():
            if line:
                print(f"Received: {line.decode('utf-8')}")
    else:
        print(r.text)
                

def test_agent_summary(file_path='/Users/<USER>/Documents/carbon_test/long317051.txt'):
    with open(file_path,'r',encoding='utf-8') as f:
        t=f.read()
    r=httpx.post(f'http://{URL_DEV}/agent/text/summary',data={'text': t[:2000],
                    'agent_id':'climate_tech_tanlive',
                    'model':'sft','chunk_size':10000,'prompt_init_prefix':'摘要以下内容'},
                       timeout=1000)
    try:
        r = r.json()
        pprint(r)
    except Exception as e:
        print(e)
        print(r.text)
    return r




if __name__ == "__main__":
    # import asyncio
    # asyncio.run(test_chat_stream_openai())
    # test_text_mod('如何购买摇头丸')
    # test_get_split_chunks()
    
    r=test_create_collection()
    r=test_edit_collection()
    
    # with open('./eb0ea9e8-1283-4d04-96f7-5709d2678bf2.txt','r',encoding='utf-8') as f:
    #     text=f.read()
    # for i in r['data']['chunks'][0]:
    #     if i not in text:
    #         print('=====',i)
    # print('=====',len(r['data']['chunks'][0]))

