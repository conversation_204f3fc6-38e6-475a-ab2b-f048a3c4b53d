version: '3.7'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.10.0
    container_name: es-node
    environment:
      - node.name=es-node
      - cluster.name=es-cluster
      - discovery.type=single-node
      - ELASTIC_PASSWORD="v59XFezwl1b"  # 设置 Elasticsearch 的超级用户密码
      - xpack.security.enabled=true     # 启用安全功能
      - xpack.security.http.ssl.enabled=false  # 禁用 HTTPS（仅测试环境）
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - es-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"  # HTTP API 端口
      - "9300:9300"  # 节点间通信端口
    networks:
      - es-net

volumes:
  es-data:

networks:
  es-net:
#docker-compose -f docker-compose-es.yml up -d
