import re


class Masker(object):
    '''
    classdocs  身份证号和手机号过滤
    '''

    def init(self):
        pass

    def do_mask(self, strline):
        self.content = strline
        self.mask_idno()
        self.mask_phone()
        return self.content

    def mask_cnname(self):
        import jieba.posseg as pseg
        jiebaRes = pseg.cut(self.content)
        for tmp in jiebaRes:
            tmp = tmp.encode('utf-8')
            m = re.findall('(.*?)\/nr', str(tmp))
            if m is not None and len(m) > 0:
                firstName = m[0].decode('utf-8')[0:1].encode('utf-8')
                self.content = re.sub(m[0].decode('utf-8').encode('utf-8'), firstName + '**', self.content)

    def mask_bankcard(self):
        m = re.findall('\D(6\d{14,18})\D', self.content)
        if m is not None and len(m) > 0:
            for one in m:
                startNum = one[0:6]
                endNum = one[-4:]
                self.content = re.sub(one, '%s*********%s' % (startNum, endNum), self.content)

    def mask_idno(self):
        m = re.findall(
            '\D([*********]\d{5}((19)|(20))\d{2}((0[*********])|(1[012]))((0[*********])|([12][0-9])|(3[01]))\d{3}[Xx0-9])(\D|$)'
            , self.content)
        print(m)
        if m is not None and len(m) > 0:
            for one in m:
                if len(one[0]) < 18:
                    continue
                startNum = one[0][0]
                endNum = one[0][-1]
                self.content = re.sub(one[0], '%s***************%s' % (startNum,
                                                                       endNum), self.content)

    def mask_phone(self):
        m = re.findall('(\D|^)(1[3578]\d{9})(\D|$)', self.content)
        if m is not None and len(m) > 0:
            for one in m[0]:
                if len(one) < 11:
                    continue
                startNum = one[0:3]
                endNum = one[-2:]
                self.content = re.sub(one, '%s******%s' % (startNum, endNum), self.content)

    def mask_email(self):
        m = re.findall(
            '(([a-zA-Z0-9]+[_|\-|\.]?)*[a-zA-Z0-9]+\@([a-zA-Z0-9]+[_|\-|\.]?)*[a-zA-Z0-9]+(\.[a-zA-Z]{2,3})+)'
            , self.content)
        if m is not None and len(m) > 0:
            for one in m:
                m1 = re.findall('(\w+)@', one[0])
                maskedOne = re.sub(m1[0], '%s******' % m1[0][0], one[0])
                self.content = re.sub(one[0], maskedOne, self.content)
