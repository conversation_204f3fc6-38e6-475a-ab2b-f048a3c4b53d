import json
import logging
import sys
import os
import datetime
import time


def ssv_logger(loggerFile):
    # 定义日志输出格式
    formattler = json.dumps({'timestamp': '%(asctime)s',
                             'path': '%(pathname)s [line:%(lineno)d]',
                             'level': '%(levelname)s',
                             'msg': '%(message)s',
                             })
    fmt = logging.Formatter(formattler)

    # logger = logging.getLogger(__name__)
    # logger.setLevel(logging.INFO)
    # log = logging.getLogger('werkzeug')
    # log.setLevel(logging.ERROR)
    logging.getLogger('werkzeug').setLevel(logging.CRITICAL)
    logging.getLogger('urllib3').setLevel(logging.CRITICAL)
    logging.getLogger('venus_api_base').setLevel(logging.CRITICAL)
    logging.getLogger('asyncio').setLevel(logging.CRITICAL)

    # 设置日志输出到控制台
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setLevel(logging.DEBUG)
    stream_handler.setFormatter(fmt)

    # 设置日志输出到文件
    file_handler = MultiprocessHandler(loggerFile)
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(fmt)

    # 增加handler日志处理器
    logging.basicConfig(level=logging.DEBUG,
                        handlers=[stream_handler, file_handler])
    # logger.addHandler(stream_handler)
    # logger.addHandler(file_handler)
    # return logger


class MultiprocessHandler(logging.FileHandler):
    """支持多进程的TimedRotatingFileHandler"""

    def __init__(self, filename, when='D', backupCount=3, delay=False):
        """
        filename 日志文件名,when 时间间隔的单位,backupCount 保留文件个数
        delay 是否开启 OutSteam缓存
        True 表示开启缓存，OutStream输出到缓存，待缓存区满后，刷新缓存区，并输出缓存数据到文件。
        False 表示不缓存，OutStrea直接输出到文件
        """
        self.suffix = filename
        self.backupCount = backupCount
        self.when = when.upper()
        # 正则匹配 年-月-日
        self.extMath = r"^\d{4}-\d{2}-\d{2}"

        # S 每秒建立一个新文件
        # M 每分钟建立一个新文件
        # H 每天建立一个新文件
        # D 每天建立一个新文件
        self.when_dict = {
            'S': "%Y-%m-%d-%H-%M-%S",
            'M': "%Y-%m-%d-%H-%M",
            'H': "%Y-%m-%d-%H",
            'D': "%Y-%m-%d"
        }
        # 日志文件日期前缀
        self.prefix = self.when_dict.get(when)
        if not self.prefix:
            raise ValueError(f'指定的日期间隔单位无效: {self.when}')

        # 拼接文件路径 格式化字符串
        self.filefmt = os.path.join("logs", "%s-%s" % (self.prefix, self.suffix))
        print(f'self.filefmt: {self.filefmt}')
        # 使用当前时间，格式化文件格式化字符串
        self.filePath = datetime.datetime.now().strftime(self.filefmt)
        print(f'self.filePath: {self.filePath}')
        # 获得文件夹路径
        _dir = os.path.dirname(self.filefmt)
        try:
            # 如果日志文件夹不存在，则创建文件夹
            if not os.path.exists(_dir):
                os.makedirs(_dir)
        except Exception:
            print("创建文件夹失败")
            print("文件夹路径：" + self.filePath)

        # 调用FileHandler
        logging.FileHandler.__init__(self, self.filePath, 'a+', delay=delay)

    def shouldChangeFileToWrite(self):
        """更改日志写入目的写入文件
        return True 表示已更改，False 表示未更改"""
        # 以当前时间获得新日志文件路径
        _filePath = datetime.datetime.now().strftime(self.filefmt)
        # 新日志文件日期 不等于 旧日志文件日期，则表示 已经到了日志切分的时候
        #   更换日志写入目的为新日志文件。
        # 例如 按 天 （D）来切分日志
        #   当前新日志日期等于旧日志日期，则表示在同一天内，还不到日志切分的时候
        #   当前新日志日期不等于旧日志日期，则表示不在
        # 同一天内，进行日志切分，将日志内容写入新日志内。
        if _filePath != self.filePath:
            self.filePath = _filePath
            return True
        return False

    def doChangeFile(self):
        """输出信息到日志文件，并删除多于保留个数的所有日志文件"""
        # 日志文件的绝对路径
        self.baseFilename = os.path.abspath(self.filePath)
        # stream == OutStream
        # stream is not None 表示 OutStream中还有未输出完的缓存数据
        if self.stream:
            # self.stream.flush()
            self.stream.close()
            self.stream = None
        # delay 为False 表示 不OutStream不缓存数据 直接输出
        # 所有，只需要关闭OutStream即可
        if not self.delay:
            # self.stream.close()
            self.stream = self._open()

        # 删除多于保留个数的所有日志文件
        if self.backupCount > 0:
            for s in self.getFilesToDelete():
                # print s
                dirName, _ = os.path.split(self.baseFilename)
                os.remove(os.path.join(dirName, s))

    def getFilesToDelete(self):
        """获得过期需要删除的日志文件"""
        dirName, _ = os.path.split(self.baseFilename)
        fileNames = os.listdir(dirName)
        fileNames.sort()

        # 返回 待删除的日志文件
        # 多于 保留文件个数 backupCount的所有前面的日志文件。
        if len(fileNames) < self.backupCount:
            result = []
        else:
            result = fileNames[:len(fileNames) - self.backupCount]
        print(result)
        return result

    def emit(self, record):
        """发送一个日志记录
        覆盖FileHandler中的emit方法，logging会自动调用此方法"""
        try:
            if self.shouldChangeFileToWrite():
                self.doChangeFile()
            logging.FileHandler.emit(self, record)
        except (KeyboardInterrupt, SystemExit):
            raise
        except:
            self.handleError(record)


if __name__ == '__main__':
    import threading


    def worker(message, ss):
        ssv_logger(message)
        worker_logger = logging.getLogger(__name__)
        for i in range(100):
            worker_logger.info(message + ' info' + ss)
            worker_logger.debug(message + ' debug' + ss)
            worker_logger.warning(message + ' warning' + ss)
            worker_logger.error(message + ' error' + ss)
            time.sleep(1)


    t1 = threading.Thread(target=worker, args=('worker', '1'))
    t2 = threading.Thread(target=worker, args=('worker', '2'))
    t1.start()
    t2.start()
