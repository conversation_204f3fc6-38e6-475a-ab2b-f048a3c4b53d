#docker build -t csighub.tencentyun.com/ssv_ml/ssv-serv:v0.4 --target serv-env .
#docker push csighub.tencentyun.com/ssv_ml/ssv-serv:v0.4

#docker build -t csighub.tencentyun.com/ssv_ml/ssv-search:v0.3 --target search-env .
#docker push csighub.tencentyun.com/ssv_ml/ssv-search:v0.3

#docker build -t csighub.tencentyun.com/ssv_ml/ssv-vec:v0.43 --target vec-env .
#docker push csighub.tencentyun.com/ssv_ml/ssv-vec:v0.43

#docker build -f Dockerfile-serv -t csighub.tencentyun.com/ssv_ml/ssv-serv:v0.33 .
#docker push csighub.tencentyun.com/ssv_ml/ssv-serv:v0.33
#
#docker build -f Dockerfile-vec -t csighub.tencentyun.com/ssv_ml/ssv-vec:v0.34 .
#docker push csighub.tencentyun.com/ssv_ml/ssv-vec:v0.34

# bash build-chat.sh
# bash build-search.sh
# bash build-serv.sh
# bash build-vec.sh

# 根据docker ps的IMAGE名字kill正在运行的版本
docker ps | grep rag/vchat | awk '{print $1}' | xargs docker kill
# build
docker build -t rag/vchat:$1 .
# run
docker run -d -v /opt/dev/logs:/app/logs -p 8001:8001 rag/vchat:$1