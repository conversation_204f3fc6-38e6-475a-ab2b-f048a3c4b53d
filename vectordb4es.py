import logging
import traceback
import httpx
import json
import hashlib
from typing import Optional, <PERSON><PERSON>
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from config import R_MODELS, ES_CONFIGS
from utils.model_logger import log_model_call
logger = logging.getLogger(__name__)

# from langchain_community.embeddings import HuggingFaceEmbeddings
# embedding = HuggingFaceEmbeddings(model_name='./models/text2vec-base-chinese/')
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk

es_mappings = { 'zh':
                {'id': {'type': 'keyword'},
                'vector': {'type': 'dense_vector', 'dims': 768},
                'tags': {'type': 'text'},
                'doc_name': {'type': 'text'},
                'user_name': {'type': 'text'},
                'question': {'type': 'text'},
                'content': {'type': 'text'}},
            'en':
                {'id': {'type': 'keyword'},
                'vector': {'type': 'dense_vector', 'dims': 384},
                'doc_name': {'type': 'text'},
                'user_name': {'type': 'text'},
                'question': {'type': 'text'},
                'content': {'type': 'text'},
                'tags': {'type': 'text'}},
            'bge-m3':
                {'id': {'type': 'keyword'},
                'vector': {'type': 'dense_vector', 'dims': 1024},
                'doc_name': {'type': 'text'},
                'user_name': {'type': 'text'},
                'question': {'type': 'text'},
                'content': {'type': 'text'},
                'tags': {'type': 'text'}}
}

def es_get_instance(es_ins):
    es_ins = 'ldxkujv6' if not es_ins else es_ins
    es_config = ES_CONFIGS[es_ins]
    es = Elasticsearch(f"http://{es_config['ip']}:{es_config['port']}",
                       basic_auth=(es_config['user'],es_config['password']))
    return es


def es_create_collection(collection_name, es_ins=None, lang='zh', n_shard=1):
    es = es_get_instance(es_ins)
    if not es.indices.exists(index=collection_name):
        settings = {'number_of_shards': n_shard}
        es.indices.create(index=collection_name,
                          body={'mappings':{'properties':es_mappings[lang]},
                            'settings':settings})
        info=f'create collection {collection_name} success'
    else:
        info=f'collection {collection_name} already exists'
    return info


def es_delete_collection(collection_name, es_ins=None):
    es = es_get_instance(es_ins)
    if not es.indices.exists(index=collection_name):
        return f'collection {collection_name} does not exist'
    es.indices.delete(index=collection_name)
    return f'delete collection {collection_name} success'


def es_bulk_upsert(collection_name, es_ins=None, 
                   documents: Optional[list] = None):
    es = es_get_instance(es_ins)
    operations = []
    for doc in documents:
        _doc = {
            "_op_type": 'index',
            "_index": collection_name,
            "_id": doc.get('id')
        }
        _doc.update(doc)
        operations.append(_doc)
    success, errors = bulk(es, operations)
    if errors:
        logger.error(f'es bulk upsert errors: {errors}')
    return f'upsert document {collection_name} success, {success}'


def es_search(collection_name, es_ins=None, 
              query_dict: dict = {'match_all': {}}, 
              vector: list = None,
              size: int = 10, from_: int = 0, 
              min_score: float = 0, total_hits: bool = False, 
              text_weight: float = 0,
              sort=None, session_id=''):
    if min_score is None:
        min_score = 0
    if from_ is None:
        from_ = 0
    if size is None:
        size = 10
    if query_dict is None:
        query_dict = {'match_all': {}}
    es = es_get_instance(es_ins)
    if not vector:
        script_query = query_dict
    elif text_weight > 0.00001:
        script_query = {
            'script_score': {
                'query': query_dict,
                'script': {
                    'source': """
                    double text_score = _score; 
                    double vector_score = cosineSimilarity(params.query_vector, 'vector');
                    return Math.max(0, 
                        params.text_weight * text_score + params.vector_weight * vector_score);
                    """,
                    'params': {
                        'query_vector': vector,
                        'text_weight': text_weight,
                        'vector_weight': 1-text_weight
                    }
                }
            }
        }
    else:
        script_query = {
            'script_score': {
                'query': query_dict,
                'script': {
                    "source": "Math.max(0, cosineSimilarity(params.query_vector, 'vector'))",
                    "params": {"query_vector": vector}
                }
            }
        }
    query_body = {
        'query': script_query,
        'size': size,
        'from_': from_,
        'min_score': min_score,
        'track_total_hits': total_hits
    }
    if sort is not None:
        query_body['sort'] = sort
    logger.info(f'es search query_body: {query_body}')
    response = es.search(index=collection_name, body=query_body)
    documents = []
    for hit in response['hits']['hits']:
        doc = hit['_source']
        doc['_score'] = hit['_score']
        documents.append(doc)
    
    if total_hits:
        return documents, response['hits']['total']['value']
    else:
        return documents
    

def es_get_document(collection_name, es_ins=None, ids: list = None):
    es = es_get_instance(es_ins)
    ret = es.get(index=collection_name, id=ids)
    return ret['_source']


def es_delete_document(collection_name, es_ins=None, ids: [list,str] = None):
    es = es_get_instance(es_ins)
    if isinstance(ids, list):
        ret = es.delete_by_query(index=collection_name, body={'query': {'terms': {'_id': ids}}})
    else:
        ret = es.delete(index=collection_name, id=ids)
    return ret


def cos_dist(vec1, vec2):
    sim = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
    return sim

# 计算两个文本的相似度（根据lang参数提取embedding，然后计算cosine sim）
def calc_text_sim(text1, text2, lang='zh'):
    embeds = _get_vectors([text1, text2], lang)
    sim = cos_dist(embeds[0], embeds[1])
    return sim


@retry(stop=stop_after_attempt(5), wait=wait_fixed(0.2), 
       retry=retry_if_exception_type(Exception))
def _ha_request(url, data, timeout, session_id=''):
    return httpx.post(url, json=data, timeout=timeout)


def _search_most_similar_es(collection_name, text: str, es_ins=None, lang='zh', 
                            query_dict: dict = {'match_all': {}}, text_weight=0,
                            top_n=10, threshold=0.0, from_=0, total_hits=False, session_id=''):
    # 1. embedding
    embed = None
    if text_weight < 0.9999:
        try:
            t_url,t_path = R_MODELS['emb_'+lang]['url'],R_MODELS['emb_'+lang]['path']
            if t_path == 'api/embed':
                embed = _ha_request(url=f'{t_url}/{t_path}', data={'model':'bge-m3','input':text}, timeout=5, session_id=session_id)
                embed = embed.json()['embeddings']
            else:    
                embed = _ha_request(url=f'{t_url}/{t_path}', data={'text':text}, timeout=2, session_id=session_id)
                embed = embed.json()['vec']
            if type(embed[0])==list:
                embed = embed[0]
        except Exception as e:
            logger.error(f'_search_most_similar_es, {lang} embeding err: '
                        f'{traceback.format_exc()}')
            raise ValueError('search most similar, emb request err')
    
    # 2. query
    if text_weight > 0.0001:
        query_dict = {
            'bool': {
                'should': [
                    {'match': {'content': text}},
                    {'match': {'question': text}}
                ]
            }
        }
    try:
        ret = es_search(collection_name, es_ins, vector=embed, query_dict=query_dict, 
                        size=top_n, from_=from_, total_hits=total_hits, 
                        text_weight=text_weight, session_id=session_id)
        if total_hits:
            res, n_total_hits = ret
        else:
            res = ret
    except Exception as e:
        logger.error(f'_search_most_similar_es, es search err: {traceback.format_exc()}')
        raise ValueError(f'search most similar, es request err:{e}')

    # 3. post process
    docs = []
    if res:
        for doc in res:
            if doc['_score'] >= threshold:
                docs.append(doc)
    if docs:
        logger.info(f'_search_most_similar_es, find doc num: {len(docs)}')
        content4log=docs[0]["content"].replace('\n',' ')
        logger.info(
            f'_search_most_similar_es query content: {text}, ||'+
            f'find most similar index content: {content4log}...')
    else:
        n_total_hits = 0

    # 4. return
    if not total_hits:
        return docs
    else:
        return docs, n_total_hits



def _get_vectors(contents, lang):
    # try:
    t_url,t_path = R_MODELS['emb_'+lang]['url'],R_MODELS['emb_'+lang]['path']
    if t_path == 'api/embed':
        vectors = _ha_request(f'{t_url}/{t_path}', {'model':'bge-m3','input':contents}, 600)
    else:
        vectors = _ha_request(f'{t_url}/{t_path}',{'text':contents},6000)
    try:
        vectors = vectors.json()
    except Exception as e:
        raise ValueError(f'embedding request err e:{vectors.text},{contents}')
    if 'vec' not in vectors and 'embeddings' not in vectors:
        raise ValueError(f'embedding request err: {vectors}')
    vectors = vectors['embeddings'] if t_path == 'api/embed' \
            else vectors['vec']
    print(f'embedding, vectors shape: {len(vectors),len(vectors[0])}')
    # except Exception as e:
    #     raise ValueError(f'upsert doc embedding err: {e}, size {len(contents)}')
    return vectors


def delete_collection_es(collection_name, es_ins):
    try:
        ret = es_delete_collection(collection_name, es_ins)
        logger.info(f'delete collection: {collection_name}')
        return ret
    except:
        logger.error(f'delete collection err: {traceback.format_exc()}')
        raise ValueError('es delete err, please retry')


def upsert_document_es(user_name, collection_name, doc_name, 
                       contents, index_contents = None,
                       es_ins = None, lang = 'zh',
                       doc_ids: Optional[list] = None,
                       vectors: Optional[list] = None):
    # 1. create
    logger.info(f'upsert document es, total {len(contents)} chunks')
    try:
        es_create_collection(collection_name, es_ins=es_ins, lang=lang)
    except Exception as e:
        raise

    # 2. get vectors
    if not vectors:
        if index_contents:
            vectors = _get_vectors(index_contents, lang)
        else:
            vectors = _get_vectors(contents, lang)

    # 3. gen es data
    documents = []
    doc_id_set = set()
    assert len(contents) == len(vectors)
    if doc_ids:
        assert len(doc_ids) == len(contents)
    for i in range(len(contents)):
        if type(contents[i]) == str:
            contents[i] = contents[i].strip()
            question = index_contents[i] if index_contents else ''
            ans = contents[i]
        elif type(contents[i]) in (list, tuple):
            question = contents[i][0]
            ans = contents[i][1]

        doc_id = hashlib.md5((doc_name+ans).encode()).hexdigest() \
                    if not doc_ids else doc_ids[i]
        if doc_id in doc_id_set or not len(contents[i]):  # 去重
            doc_id = None
            continue
        else:
            doc_id_set.add(doc_id)

        documents.append({'id': doc_id,
                          'vector': vectors[i],
                          'doc_name': doc_name,
                          'user_name': user_name,
                          'question': question,
                          'content': ans})

        ans4log=ans.replace('\n',' ')[:20]
        logger.info(
            f'upsert document es, doc id: {doc_id}, len: {len(ans)}, '+
            f'collection: {collection_name}, content: {ans4log}...')
        doc_id = None

    if not documents:
        logger.info(f'upsert document, collection `{collection_name}` with no content')
        raise ValueError('empty document, nothing to upsert')

    # 4. upsert
    try:
        es_bulk_upsert(collection_name, es_ins, documents)
        logger.info(f'upsert document, collection `{collection_name}` '+
                    f'total {len(documents)} docs successfully')
    except Exception as e:
        logger.error(f'upsert document es err: {traceback.format_exc()}')
        raise ValueError(f'es upsert err: {e}')
    return list(doc_id_set)


def update_document_es(user_name, collection_name, content, es_ins=None, lang='zh',
                        index_content=None, doc_name='', threshold=0.6):
    # ONLY in edit scenario
    docs = []
    if index_content:
        try:
            docs = _search_most_similar_es(collection_name, 
                                            index_content, 
                                            es_ins=es_ins,
                                            lang=lang,
                                            threshold=threshold)
        except Exception as e:
            if 'no such index' in str(e):
                n_shard = 1
                es_create_collection(collection_name, es_ins=es_ins, 
                                     lang=lang, n_shard=n_shard)
                docs=[]
            else:
                logger.info(f'update document es, search similar docs err: '
                            f'{traceback.format_exc()}')
                raise

    # 对找到的Document进行修改
    if len(docs) > 0:# and docs[0]["question"] and question == docs[0]["question"]:
        doc_id = docs[0]['id']
        doc_vec = docs[0]['vector']
        doc_content = content
        # doc_content = docs[0]['content']
        print(f'update document es, doc id: {doc_id}, doc_name: {doc_name}')
        logger.info(f'update document, doc id: {doc_id}')
        index_content = index_content if index_content else ''
        try:
            affected_ids = upsert_document_es(user_name=user_name,
                           collection_name=collection_name,
                           doc_name=doc_name,
                           contents=[doc_content],
                           index_contents=[index_content] if index_content else None,
                           es_ins=es_ins,
                           doc_ids=[doc_id],
                           vectors=[doc_vec])
        except:
            raise ValueError(f'update doc err: {e}')
        logger.info(f'update document result, doc id: {doc_id}, '
                    f'content: {doc_content[:20]}...')
        return affected_ids, doc_content

    # 创建新的entry
    else:
        index_content = index_content if index_content else content
        logger.info(f'update document, upsert new:{index_content}')
        doc_id = hashlib.md5((doc_name+index_content).encode()).hexdigest()
        try:
            embed = _get_vectors([index_content], lang)
        except Exception as e:
            raise ValueError(f'update document, embedding err: {traceback.format_exc()}')
        print('indexed content',index_content)
        print('updating new calc vectors:',embed[:10])
        try:
            affected_ids = upsert_document_es(user_name=user_name,
                           collection_name=collection_name,
                           doc_name=doc_name,
                           contents=[content],
                           index_contents=[index_content] if index_content!=content else None,
                           es_ins=es_ins,
                           lang=lang,
                           doc_ids=[doc_id],
                           vectors=embed)
        except:
            raise
        return affected_ids, 'NEW CREATED! no matched doc found'


def delete_documents_es(collection_name, es_ins, ids) -> None:
    if not ids:
        logger.info(f'delete document, empty ids, pass')
        raise ValueError('delete document without ids')
    try:
        ret = es_delete_document(collection_name, es_ins, ids)
        logger.info(f'delete document ret: {ret}')
    except:
        raise
    return ids


def get_documents_es(collection_name, es_ins, doc_name, size=None, from_=None, 
                     min_score=None, total_hits=False, session_id='') -> Tuple[list[str], list[str]]:
    query = {"match_phrase": {"doc_name": doc_name}}
    # 没有doc_name，则返回所有文档
    if not doc_name:
        query = {"match_all": {}}
    try:
        sort = [{'_score': 'desc'}, {'id': 'asc'}]
        docs = es_search(collection_name=collection_name, es_ins=es_ins, query_dict=query,
                        size=size, from_=from_, total_hits=total_hits, min_score=min_score, sort=sort, session_id=session_id)
        if total_hits:
            docs, n_total_hits = docs
        if not docs:
            logger.error(f'get document ret empty: {docs}')
            if total_hits:
                return [], [], 0
            else:
                return [], []
        # docs = ret['documents']
    except Exception as e:
        logger.error(f'get document query err: {traceback.format_exc()}')
        raise ValueError(f'get document query err: {traceback.format_exc()}')
    # end rewrite
    ret_docs, ids = [], []
    if doc_name:
        for doc in docs:
            if doc['doc_name'] != doc_name:
                logger.error(f'get document match doc_name err: '
                             f'{doc["doc_name"]}, vs {doc_name}')
                continue
            ids.append(doc['id'])
            ret_docs.append({i:doc[i] for i in doc if i not in ['vector']})
    else:
        for doc in docs:
            ids.append(doc['id'])
            ret_docs.append({i:doc[i] for i in doc if i not in ['vector']}) 
    if total_hits:
        logger.info(f'get documents result: {ret_docs}, ids: {ids}, '
                    f'total_hits: {n_total_hits}')
        return ret_docs, ids, n_total_hits
    else:
        logger.info(f'get documents result: {ret_docs}, ids: {ids}')
        return ret_docs, ids


def get_documents_byid_es(collection_name, es_ins, ids) -> Tuple[list[str], list[str]]:
    data = {'ids':ids}
    try:
        # ret = _ha_request(f'{ES_PROXY_URL}/document/get', data, 2000)
        # ret = json.loads(ret.text)
        es = es_get_instance(es_ins)
        ret = es.mget(index=collection_name, body=data)
        print(f'get documents by id ret: {ret}')
        docs = ret['docs']
    except Exception as e:
        logger.error(f'get document query err: {traceback.format_exc()},{ret}')
        raise ValueError(f'get document query err: {traceback.format_exc()}')
    logger.info(f'get documents id result: {docs}, ids: {ids}')
    return docs


def update_documents_byid_es(collection_name, es_ins, lang, documents) -> None:
    '''document : {'id': doc_id,
                    'vector': vectors[i],
                    'doc_name': doc_name,
                    'user_name': user_name,
                    'question': question,
                    'content': ans}
    '''
    
    for doc in documents:
        doc['vector'] = _get_vectors([doc['content']], lang)[0]

        ans4log=doc['content'].replace('\n',' ')[:20]
        logger.info(
            f'update document(byid) es, doc id: {doc["id"]}, '+
            f'collection: {collection_name}, content: {ans4log}...')

    if not documents:
        logger.info(f'update document(byid), collection `{collection_name}` with no content')
        raise ValueError('empty document, nothing to upsert')

    try:
        es_bulk_upsert(collection_name, es_ins, documents)
        logger.info(f'update document(byid), collection `{collection_name}` '+
                    f'total {len(documents)} docs successfully')
    except Exception as e:
        logger.error(f'update document(byid) es err: {traceback.format_exc()}')
        raise ValueError(f'es upsert err: {e}')
    return [doc['id'] for doc in documents]


def search_documents_es(collection_name, question, 
                        es_ins=None, lang='zh',
                        query_dict: dict = {'match_all': {}},
                        top_n=10, threshold=0, 
                        from_=0, total_hits=False, text_weight=0, session_id='') -> list[dict]:
    try:
        docs = _search_most_similar_es(collection_name,
                                   question,
                                   es_ins=es_ins,
                                   lang=lang,
                                   query_dict=query_dict,
                                   top_n=top_n,
                                   threshold=threshold,
                                   from_=from_,
                                   total_hits=total_hits,
                                   text_weight=text_weight,
                                   session_id=session_id
                                   )
    except Exception as e:
        raise
    if total_hits:
        docs, n_hits = docs
    ret = []
    for doc in docs:
        # 如果content以[INH:开头，以]结尾，则认为是一个继承的doc，需要获取父doc
        if doc['content'].startswith('[INH:') and doc['content'].endswith(']'):
            inherted_id = doc['content'][5:-1]
            parent_doc = get_documents_byid_es(collection_name, es_ins, inherted_id)
            # 如果父doc存在，则将父doc作为当前实际doc
            if parent_doc:
                doc = parent_doc
        ret.append({'id': doc['id'],
                    'question': doc['question'],
                    'content': doc['content'],
                    'score': round(doc['_score'], 3),
                    'doc_name': doc['doc_name'], })
    # ret = [i for i in ret if i['score']>threshold]
    ret4log = str(ret).replace('\n',' ')
    logger.info(f'search documents result: {ret4log}')
    if total_hits:
        return ret, n_hits
    else:
        return ret

