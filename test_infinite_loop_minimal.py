#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小复现测试：enhanced_recursive_split 第129-131行无限循环
"""

import sys
import os
import signal
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.text_processing import enhanced_recursive_split


class TimeoutError(Exception):
    pass


def timeout_handler(signum, frame):
    raise TimeoutError("函数执行超时")


def test_infinite_loop_minimal():
    """最小复现测试：触发第129-131行无限循环"""
    
    print("=== 最小复现测试：第129-131行无限循环 ===")
    print("问题代码位置：utils/text_processing.py 第129-131行")
    print("while start > 0 and chunk[start-1] == chunk[end-1]:")
    print("    start -= 1")
    print("    end -= 1")
    print()
    
    # 测试用例1：最小重复字符
    test_cases = [
        {
            "name": "最小重复字符(10个a)",
            "text": "a" * 10,
            "chunk_size": 5,
            "chunk_overlap": 2
        },
        {
            "name": "中等重复字符(100个a)", 
            "text": "a" * 100,
            "chunk_size": 50,
            "chunk_overlap": 10
        },
        {
            "name": "重复中文字符",
            "text": "中" * 50,
            "chunk_size": 25,
            "chunk_overlap": 5
        },
        {
            "name": "重复数字",
            "text": "1" * 30,
            "chunk_size": 15,
            "chunk_overlap": 3
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"测试 {i}: {case['name']}")
        print(f"  文本: '{case['text'][:20]}{'...' if len(case['text']) > 20 else ''}'")
        print(f"  长度: {len(case['text'])}")
        print(f"  chunk_size: {case['chunk_size']}, chunk_overlap: {case['chunk_overlap']}")
        
        # 设置3秒超时
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(100)
        
        try:
            start_time = time.time()
            result = enhanced_recursive_split(
                case['text'],
                separators=["\n", ".", ","],  # 这些分隔符在文本中不存在
                chunk_size=case['chunk_size'],
                chunk_overlap=case['chunk_overlap']
            )
            end_time = time.time()
            signal.alarm(0)
            
            print(f"  ✓ 完成，耗时: {end_time - start_time:.3f}秒")
            print(f"  结果块数: {len(result)}")
            if result:
                print(f"  第一块: '{result[0][:20]}{'...' if len(result[0]) > 20 else ''}'")
                print(f"  最后块: '{result[-1][:20]}{'...' if len(result[-1]) > 20 else ''}'")
            
        except TimeoutError:
            signal.alarm(0)
            print(f"  ✗ 超时！这证实了无限循环问题")
            print(f"  原因：文本全为相同字符，chunk[start-1] == chunk[end-1] 始终为真")
            
        except Exception as e:
            signal.alarm(0)
            print(f"  ✗ 错误: {e}")
        
        print()


def explain_infinite_loop():
    """解释无限循环的原理"""
    print("=== 无限循环原理解释 ===")
    print()
    print("1. 触发条件：")
    print("   - 文本由相同字符组成（如 'aaaaaaa'）")
    print("   - 没有找到指定的分隔符")
    print("   - 进入固定长度切分逻辑（第118-134行）")
    print()
    print("2. 问题流程：")
    print("   a) 计算切分位置：end = start + chunk_size")
    print("   b) 计算重叠：start = end - chunk_overlap") 
    print("   c) 进入while循环检查重复：")
    print("      while start > 0 and chunk[start-1] == chunk[end-1]:")
    print()
    print("3. 无限循环原因：")
    print("   - 对于文本 'aaaaaaa'，任意位置的字符都是 'a'")
    print("   - chunk[start-1] 和 chunk[end-1] 始终都是 'a'")
    print("   - 条件 chunk[start-1] == chunk[end-1] 永远为真")
    print("   - start 和 end 会一直减少，直到 start = 0")
    print()
    print("4. 最坏情况：")
    print("   - 如果 chunk_overlap 很大，循环次数可能接近 chunk_size")
    print("   - 每次循环都要进行字符比较，累积时间很长")
    print()


def demonstrate_loop_behavior():
    """演示循环行为"""
    print("=== 演示循环行为 ===")
    
    # 模拟第129-131行的逻辑
    chunk = "aaaaaaaaaa"  # 10个a
    chunk_size = 6
    chunk_overlap = 3
    
    print(f"文本: '{chunk}'")
    print(f"chunk_size: {chunk_size}, chunk_overlap: {chunk_overlap}")
    print()
    
    # 模拟第一次切分
    start = 0
    end = min(start + chunk_size, len(chunk))  # end = 6
    print(f"第一块: '{chunk[start:end]}' (start={start}, end={end})")
    
    # 模拟第二次切分的准备
    start = end - chunk_overlap  # start = 6 - 3 = 3
    end = min(start + chunk_size, len(chunk))  # end = min(9, 10) = 9
    print(f"第二块准备: start={start}, end={end}")
    print(f"chunk[start-1] = chunk[{start-1}] = '{chunk[start-1]}'")
    print(f"chunk[end-1] = chunk[{end-1}] = '{chunk[end-1]}'")
    print(f"相等吗？{chunk[start-1] == chunk[end-1]}")
    print()
    
    # 模拟while循环
    print("模拟while循环：")
    loop_count = 0
    max_loops = 10  # 防止真的无限循环
    
    while start > 0 and chunk[start-1] == chunk[end-1] and loop_count < max_loops:
        print(f"  循环 {loop_count + 1}: start={start}, end={end}, chunk[{start-1}]='{chunk[start-1]}', chunk[{end-1}]='{chunk[end-1]}'")
        start -= 1
        end -= 1
        loop_count += 1
    
    if loop_count >= max_loops:
        print(f"  ... (为防止真的无限循环，这里停止)")
        print(f"  实际情况下，这会一直循环到 start=0")
    else:
        print(f"  循环结束: start={start}, end={end}")


if __name__ == "__main__":
    explain_infinite_loop()
    demonstrate_loop_behavior()
    test_infinite_loop_minimal()
