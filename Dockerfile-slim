
# FROM python:3.11.11-bullseye AS serv-env
FROM python:3.11-slim-bullseye AS serv-env

ENV LANG zh_CN.UTF-8
ENV LC_CTYPE zh_CN.UTF-8
#ADD  data /app/data
COPY __init__.py gpt3.py llms.py config.py vec.py uvi_deploy.py vectordb4es.py uvi_requirements_slim.txt ssv_logger.py /app/
COPY /utils /app/utils
WORKDIR /app

#RUN curl http://openapi.zhiyan.woa.com/zhiyan_agent_install_shell/upgrade_zhiyan_agent.sh |xargs -0 -i sh -c {} -a log
RUN mkdir /var/log/serv && \
    chmod 777 /app
# RUN mkdir -p /root/nltk_data
# RUN tar -xf ./nltk_data.tar && cp -r ./nltk_data/* /root/nltk_data/ && rm -r ./nltk_data

RUN pip install --upgrade pip==22.2.2 && \
  pip install --no-cache-dir -r uvi_requirements_slim.txt -i https://mirrors.tencent.com/pypi/simple/ &&\
  pip install textract -i https://mirrors.tencent.com/pypi/simple/ &&\
  pip install pdfplumber -i https://mirrors.tencent.com/pypi/simple/

EXPOSE 8001

ENTRYPOINT ["python3", "uvi_deploy.py"]
