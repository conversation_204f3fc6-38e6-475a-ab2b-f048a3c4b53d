#encoding=utf8
import logging
import os
import uuid
import httpx
import hashlib
import threading
import time
import re
import json
import traceback
from typing import Optional, List
from ssv_logger import ssv_logger
ssv_logger('vec.log')
logger = logging.getLogger(__name__)

# 预加载数据集
# import nltk
# 添加本地资源路径到nltk.data.path
# local_data_path = "/root/nltk_data"
# nltk.data.path.insert(0, "./nltk_data")
# nltk.data.path.insert(0, local_data_path)  # 插入到首位
# nltk.download('punkt')
# nltk.download('punkt_tab')
# nltk.download('averaged_perceptron_tagger')
# nltk.download('averaged_perceptron_tagger_eng')

# 设置多核运行
# import torch
# import multiprocessing
# os.environ["TOKENIZERS_PARALLELISM"] = "false"
# torch.set_num_threads(multiprocessing.cpu_count())

from fastapi import FastAPI, Depends
# from langchain.text_splitter import RecursiveCharacterTextSplitter
from utils.query_parser import parse_fastapi_data, generate_session_id
from utils.text_processing import enhanced_recursive_split, merge_lines
from config import R_MODELS, V_BATCHSIZE, RECURSIVE_SPLIT_SEPARATORS, \
    CHUNK_SEG_THRESHOLD, CHUNK_SEG_EMB_MODEL, CHUNK_SEG_SPLITTERS
from vectordb4es import es_create_collection, es_delete_collection, upsert_document_es,\
        update_document_es, get_documents_es, delete_documents_es, search_documents_es,\
        get_documents_byid_es, update_documents_byid_es, calc_text_sim, cos_dist
# from functools import wraps
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type


@retry(stop=stop_after_attempt(5), wait=wait_fixed(0.2), 
        retry=retry_if_exception_type(Exception))
def _ha_request(es_path, data, timeout):
    return httpx.post(es_path, json=data, timeout=timeout)


try:
    from config import SQL_HOST, SQL_DB, SQL_PROGRESS_TABLE, SQL_U, SQL_PW
    from mysql.connector import pooling
    dbconfig = {
        "host": SQL_HOST,
        "database": SQL_DB,
        "user": SQL_U,
        "password": SQL_PW
    }
    connection_pool = pooling.MySQLConnectionPool(pool_name="sqlpool",
                                                  pool_size=5,
                                                  **dbconfig)
except:
    connection_pool = None

vec_app = FastAPI(debug=True,docs_url=None, redoc_url=None)

TEMP_PATH = f'{os.getcwd()}/temp'
if not os.path.exists(TEMP_PATH):
    os.mkdir(TEMP_PATH)



def split_texts_for_collection(data):
    """Preprocess text data for collection creation
    Args:
        data (dict): Input data containing:
            - chunk_size (int, optional): Size of text chunks
            - chunk_overlap_size (int, optional): Overlap between chunks
            - chunk_min_size (int, optional): Minimum chunk size
            - lang (str, optional): Language code ('zh', 'en', etc)
            - texts (list): List of text data to process
    Returns:
        tuple: (documents, failed)
    """
    # 用户自定义列表分块，第一优先级
    if data.get('text') and isinstance(data['text'], list):
        file_name = data.get('file_name', f'tmp_{generate_session_id()[-5:]}.txt')
        documents = [[file_name] + data['text']]
        failed = []
        return documents, []
    

    # Validate and get parameters
    _chunk_size = data.get('chunk_size')
    if _chunk_size:
        try:
            _chunk_size = int(_chunk_size)
        except:
            raise ValueError("chunk_size must be an integer")

    chunk_overlap_size = data.get('chunk_overlap_size')
    if chunk_overlap_size:
        try:
            chunk_overlap_size = int(chunk_overlap_size)
        except:
            raise ValueError("chunk_overlap_size must be an integer")

    chunk_min_size = data.get('chunk_min_size')
    if chunk_min_size:
        try:
            chunk_min_size = int(chunk_min_size)
        except:
            raise ValueError("chunk_min_size must be an integer")
    if_merge_lines = data.get('merge_lines', True)
    lang = data.get('lang', 'zh')
    texts = data.get('texts')
    # 既没有text，也没有file，直接返回
    if not texts:
        return [], []

    # Set chunk parameters based on language
    if lang == 'zh':
        chunk_size = _chunk_size if _chunk_size else 320
        chunk_overlap = chunk_overlap_size if chunk_overlap_size else \
                _chunk_size//20 if _chunk_size else 10
    elif lang == 'en':
        chunk_size = _chunk_size if _chunk_size else 840
        chunk_overlap = chunk_overlap_size if chunk_overlap_size else \
                _chunk_size//20 if _chunk_size else 64
    else:
        chunk_size = _chunk_size if _chunk_size else 3072
        chunk_overlap = chunk_overlap_size if chunk_overlap_size else \
                _chunk_size//20 if _chunk_size else 128

    # 用户自定义分块，第二优先级
    chunk_splitter = data.get('chunk_splitter')
    if chunk_splitter and isinstance(chunk_splitter, str):
        documents, failed = [], []
        for text in data['texts']:
            try:
                documents.append([text[0]] + text[1].split(chunk_splitter))
            except Exception as e:
                raise ValueError(f"chunk_splitter split text err:{e}")
        return documents, []
        

    # text_splitter = RecursiveCharacterTextSplitter(
    chunk_min = chunk_min_size if chunk_min_size else max(chunk_overlap,100)
    # text_splitter = F(
    #         min_length=chunk_min,
    #         separators=['##','\n\n','\n','。','.',' ',''],
    #         chunk_size=chunk_size, chunk_overlap=chunk_overlap)

    documents, failed = [], []
    for file in texts:
        filename = file[0]
        document = [filename]
        try:
            f_texts = merge_lines(file[1]) if if_merge_lines else file[1]
            f_texts = enhanced_recursive_split(f_texts, 
                                    separators=RECURSIVE_SPLIT_SEPARATORS,
                                    chunk_size=chunk_size, 
                                    chunk_overlap=chunk_overlap, 
                                    chunk_min=chunk_min)
            logger.debug(f'split_texts_for_collection splitted size: {len(f_texts)}')
            # buffer = ''
            # for text in f_texts:
            #     if isinstance(text,str):
            #         buffer += text.strip()
            #     else:
            #         buffer += text.page_content.strip()
            #     if len(buffer)>chunk_min:
            #         document.append(buffer)
            #         buffer=''
            # document.append(buffer)
            logger.info(f'split_texts_for_collection filename: {filename},'
                        f'{len(document)-1} segs: {document[:10]}...')
            documents.append(document + f_texts)
        except Exception as e:
            logger.error(f'split_texts_for_collection failed: {filename},'
                         f'{traceback.format_exc()}')
            failed.append(filename)

    return documents, failed


@vec_app.post('/get_split_chunks')
def get_split_chunks(data: dict = Depends(parse_fastapi_data)):
    print('=====texts len:',len(data['texts'][0]))
    documents, failed = split_texts_for_collection(data)
    if len(documents) == 0:
        return {'code':90001,'info':'no chunks'}
    return {'code':10000,'info':'',
            'data':{'chunks':documents}}


@vec_app.post('/t2vec')
def t2vec(data: dict = Depends(parse_fastapi_data)):
    logger.info(f't2vec request data: {data}')
    s1 = data['text']
    lang = data['lang'] if 'lang' in data else 'zh'
    t_url,t_path = R_MODELS['emb_'+lang]['url'],R_MODELS['emb_'+lang]['path']
    if type(s1) in (str, list):
        try:
            if t_path == 'api/embed':
                embeds = _ha_request(f'{t_url}/{t_path}', 
                                        {'model':'bge-m3','input':s1}, 5)
                embeds = embeds.json()['embeddings']
            else:
                embeds = _ha_request(f'{t_url}/{t_path}',{'text':s1},5)
                embeds = embeds.json()['vec']
        except:
            return {}
    else:
        embeds = []
    ret = {'code':10000,'info':'','vec':embeds}
    logger.info(f't2vec, {s1} embeds: {embeds}')
    return ret



def write_progress(sid, progress, seg_ids):
    current_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
    progress = json.dumps(progress)
    seg_ids = json.dumps(seg_ids) if type(seg_ids)!=str else seg_ids
    # cursor = sql_progress_conn.cursor()

    if connection_pool:
        try:
            sql_progress_conn = connection_pool.get_connection()
            cursor = sql_progress_conn.cursor()
            cursor.execute(f"SELECT sid FROM {SQL_PROGRESS_TABLE} WHERE sid = '{sid}'")
            result = cursor.fetchone()
            # 如果 sid 不存在，则插入新行
            if result is None:
                insert_query = f"""
                INSERT INTO {SQL_PROGRESS_TABLE} 
                (progress, seg_ids, update_time, sid, create_time)
                VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(insert_query, 
                               (progress, seg_ids, current_time, sid, current_time))
            else:
                # 如果 sid 存在，则更新 update_time
                update_query = f"""
                UPDATE {SQL_PROGRESS_TABLE}
                SET progress = %s, seg_ids = %s, update_time = %s
                WHERE sid = %s
                """
                cursor.execute(update_query, (progress, seg_ids, current_time, sid))
            # 提交更改
            sql_progress_conn.commit()
        except:
            logger.error(f'write progress, sql pool err:{traceback.format_exc()}')
            if sql_progress_conn.in_transaction:
                sql_progress_conn.rollback()  # 回滚事务
            return
        finally:
            # 确保游标被关闭
            if cursor:
                cursor.close()
        
        if sql_progress_conn:
            sql_progress_conn.close()

    else:
        logger.error(f'write progress, sql pool not ready')
        return



@vec_app.post('/create_async')
@vec_app.post('/update_async')
def create_collection_async(data: dict = Depends(parse_fastapi_data)):
    session_id = data.get('session_id')
    instant_return_thread = threading.Thread(target=do_create_collection,
                                             kwargs={'data':data})
    instant_return_thread.start()
    return {'code': 10000, 'session_id': session_id,
            'info':f'{session_id} submitted, processing',
            'data':{}}



@vec_app.post('/create')
@vec_app.post('/update')
def create_collection(data: dict = Depends(parse_fastapi_data)):
    ret = do_create_collection(data)
    return ret


def do_create_collection(data):
    sid = data.get('session_id', 'UndefinedSID')
    user_name = data.get('user_name','tester')
    collection_name = data.get('collection_name', None)
    vdb_type = data.get('vdb_type') if data.get('vdb_type') else 'es'
    es_ins = data.get('es_ins') if data.get('es_ins') else 'ldxkujv6'
    n_shard = data.get('n_shard', 1)
    lang = data.get('lang', 'zh')
    return_ids = data.get('return_ids', False)
    return_chunks_only = data.get('return_chunks_only', False)

    # Validate inputs
    if n_shard:
        try:
            n_shard = int(n_shard)
        except:
            return {'code': 90001, 
                    'info': f'ERROR upsert: n_shard must be an integer'}

    if not collection_name and not return_chunks_only:
        return {'code': 90001, 
                'info': f'ERROR upsert collection request: collection name unknown'}

    # Process text data
    documents, failed = [], []
    if 'texts' in data or isinstance(data.get('text'), list):
        try:
            documents, failed = split_texts_for_collection(data)
            success = []  # 记录成功构建索引的文件名
            logger.info(f'[{sid}]create collection, upsert, text done: {collection_name}')
        except ValueError as e:
            return {'code': 90001, 'info': f'ERROR preprocessing text: {str(e)}'}
        except Exception as e:
            logger.error(f'[{sid}]preprocess text err:{traceback.format_exc()}')
            return {'code': 90001, 'info': f'ERROR preprocessing text: {str(e)}'}

    if return_chunks_only:
        return {'code': 10003, 
                'data': {'chunks':documents}}

    # 没有文件，则只需要创建collection
    if not documents and not failed:
        try:
            logger.info('[{sid}]create collection, no files to load, just create')
            es_create_collection(collection_name=collection_name,
                                es_ins=es_ins, lang=lang, n_shard=n_shard)
            return {'code': 10002, 
                    'info': f'collection: {collection_name} create successfully.'}
        except Exception as e:
            logger.error(f'[{sid}]create collection err:{traceback.format_exc()}')
            return {'code': 90001, 'info': f'upsert collection request err: {e}'}

    all_chunk_ids = []
    for document in documents:
        doc_name = document[0]
        contents = [i for i in document[1:] if i]

        # 清理同文件名chunk
        try:
            write_progress(sid,{'clean_progress':0,'upsert_progress':0,'status':1000},[])
            from_ = 0
            ret, ids, total_hits = get_documents_es(collection_name, es_ins, doc_name,
                                                    size=100, total_hits=True, session_id=sid)
            while(len(ids) < total_hits):
                from_+=100
                _ret, _ids = get_documents_es(collection_name, es_ins, 
                                            doc_name, size=100, from_=from_, session_id=sid)
                ret += _ret
                ids += _ids
            if ids:
                delete_documents_es(collection_name, es_ins, ids)
            logger.info(f'[{sid}]create collecton doc: {doc_name} exist, '
                        f'delete {len(ids)} docs: {ids}')
        except Exception as e:
            logger.info(f'[{sid}]create collection, get doc_name: {doc_name} '+
                        f'found no document in vector database, {traceback.format_exc()}')

        # 写入chunk
        try:
            logger.info(f'[{sid}]create collection, upsert document: {doc_name}, '
                        f'chunk_size: {len(contents)}')
            # add batch size for es bulk limitation
            batch_size = V_BATCHSIZE if 'bge' not in lang else V_BATCHSIZE//10
            num_batches = (len(contents) + batch_size - 1) // batch_size
            for i in range(num_batches):
                batch_start_index = i * batch_size
                batch_end_index = min((i + 1) * batch_size, len(contents))
                batch_contents = contents[batch_start_index:batch_end_index]
                doc_ids = [hashlib.md5((doc_name+ans).encode()).hexdigest() 
                            for ans in batch_contents]
                if return_ids:
                    all_chunk_ids += doc_ids
                try:
                    affected_ids = upsert_document_es(user_name, collection_name, 
                                                doc_name, batch_contents, 
                                                doc_ids=doc_ids,
                                                es_ins=es_ins, lang=lang)
                    logger.error(f'[{sid}]create collection fufilled: size {len(doc_ids)}, '
                                        f'affected_ids: {affected_ids}, doc_ids: {doc_ids}')
                    if set(affected_ids)!=set(doc_ids):
                        logger.error(f'[{sid}]create collection ids unmatched!: '
                                        f'affected_ids: {affected_ids}, doc_ids: {doc_ids}')
                except Exception as e:
                    logger.error(f'[{sid}]create collection fail: {doc_name},'
                                    f'{traceback.format_exc()}')
                    write_progress(sid,
                                    {'clean_progress':1,'upsert_progress':0,
                                        'status':5000,'info':f'failed: {e}'},
                                    all_chunk_ids)
                    return {'code': 90012, 'session_id': sid,
                                'info': f'create collection upsert err:{e}'}
                upsert_progress = batch_end_index / len(contents)
                write_progress(sid,
                                {'clean_progress':1,'upsert_progress':upsert_progress, 'chunk_count':batch_end_index,
                                    'status':1000},
                                all_chunk_ids)
            success.append(doc_name)
        except Exception as e:
            failed.append(doc_name)
            write_progress(sid,
                           {'clean_progress':1,'upsert_progress':0,
                                'status':5000,'info':f'failed, please retry, {e}'},
                            [])
            logger.error(f'[{sid}]create collection fail: {doc_name},'
                         f'{traceback.format_exc()}')

    logger.info(f'[{sid}]create collection, {", ".join(success)}; '
                f'failed: {", ".join(failed)}')
    if not failed:  # 全部创建成功
        ret_data = {'code': 10000, 'session_id': sid,
                    'info': f'{", ".join(success)} create collection successfully'}
        if return_ids:
            ret_data['ids'] = all_chunk_ids
        return ret_data
    elif success:  # 部分创建成功
        ret_data = {'code': 90012, 'session_id': sid,
                    'info': f'{", ".join(success)} create collection successfully, '
                            f'{", ".join(failed)} failed to create collection'}
        if return_ids:
            ret_data['ids'] = all_chunk_ids
        return ret_data
    else:  # 全部创建失败
        return {'code': 90011, 'session_id': sid,
                'info': f'{", ".join(failed)} failed to create collection'}


@vec_app.post('/add_segments')
def add_segments(data: dict = Depends(parse_fastapi_data)):
    '''
    当一个chunk存在多个句子时，检查每相邻句子embedding距离，
    如果距离大于配置的threshold，则分出新的group，
    最后每个group计算平均embedding作为该group的embedding，
    如果group数量大于1，
    把所有group的embedding都写入知识库索引，content为[SEG:chunk_doc_id]。
    这里的embedding算法从配置文件获得，默认是zh
    '''
    collection_name = data.get('collection_name', None)
    sid = data.get('session_id', 'UndefinedSID')
    es_ins = data.get('es_ins') if data.get('es_ins') else 'ldxkujv6'
    lang = data.get('lang', CHUNK_SEG_EMB_MODEL)
    threshold = data.get('threshold', CHUNK_SEG_THRESHOLD)
    chunk_content = data.get('chunk_content', None)
    chunk_id = data.get('chunk_id', None)
    doc_name = data.get('doc_name', None)
    required_fields = [
        ('chunk_content', chunk_content, 'chunk_content'),
        ('collection_name', collection_name, 'collection name'),
        ('lang', lang, 'lang'),
        ('threshold', threshold, 'threshold'),
        ('session_id', sid, 'session_id'),
        ('doc_name', data.get('doc_name'), 'doc_name'),
        ('chunk_id', data.get('chunk_id'), 'chunk_id'),
    ]
    for key, value, desc in required_fields:
        if not value:
            return {'code': 90001,
                    'info': f'ERROR add segments: {desc} unknown',
                    'data': {}}
    # 以`. `,`。`,`\n`为分隔符，分割chunk_content
    pattern = "|".join(map(re.escape, CHUNK_SEG_SPLITTERS))
    segments = re.split(pattern, chunk_content)
    # 计算每相邻句子embedding距离，分组逻辑：连续小于阈值的seg放到同一个group，距离大于阈值时后者放到新的group
    groups = []
    if segments:
        # 获取所有segment的embedding
        # t2vec接口支持批量输入 TODO 按batch size分批
        t2vec_result = t2vec({'text': segments, 'lang': lang})
        if isinstance(t2vec_result, dict):
            embeddings = t2vec_result.get('data', [])
        else:
            embeddings = t2vec_result
        if not embeddings or len(embeddings) != len(segments):
            return {'code': 90001, 'info': 'ERROR add segments: embedding failed', 'data': {}}
        current_group = [(segments[0], embeddings[0])]
        for i in range(1, len(segments)):
            segment1 = segments[i-1]
            segment2 = segments[i]
            emb1 = embeddings[i-1]
            emb2 = embeddings[i]
            sim_result = cos_dist(emb1, emb2)
            if isinstance(sim_result, dict):
                distance = sim_result.get('data', 0)
            else:
                distance = sim_result
            if distance > threshold:
                groups.append(current_group)
                current_group = [(segment2, emb2)]
            else:
                current_group.append((segment2, emb2))
        if current_group:
            groups.append(current_group)
    if len(groups) > 1:
        # INSERT_YOUR_CODE
        # 用每个group中embedding平均做向量，[INH:chunk_id]做content，其他参数用原来chunk的参数插入知识库
        new_docs = []
        for idx, group in enumerate(groups):
            group_segments, group_embeds = zip(*group)
            # 计算embedding平均值
            avg_embed = [float(sum(x)/len(x)) for x in zip(*group_embeds)]
            # 构造新doc
            new_doc = {
                'id': str(uuid.uuid4()),
                'content': f'[INH:{chunk_id}]',
                'vector': avg_embed,
                'collection_name': collection_name,
                'lang': lang,
                'doc_name': doc_name,
                'chunk_id': chunk_id,
                'session_id': sid,
                'group_index': idx,
                'segments': list(group_segments)
            }
            new_docs.append(new_doc)
        # 插入知识库
        try:
            upsert_document_es(collection_name, new_docs, es_ins=None, lang=lang)
        except Exception as e:
            logger.error(f'add segments upsert err: {traceback.format_exc()}')
            return {'code': 90001, 'info': f'ERROR add segments: upsert failed: {e}', 'data': {}}
        return {'code': 10000, 'session_id': sid,
                'info': f'add segments: {segments}',
                'data':{}}  
    else:
        return {'code': 90001, 
                'info': f'ERROR add segments: no segments to add',
                'data':{}}


@vec_app.post('/delete')
def delete_collection(data: dict = Depends(parse_fastapi_data)):
    collection_name = data.get('collection_name', None)
    if not collection_name:
        return {'code': 90001, 
                'info': f'ERROR delete collection request: collection name unknown',
                'data':{'code':90001,
                        'info':f'ERROR delete collection request: collection name unknown'}}
    vdb_type = data.get('vdb_type') if data.get('vdb_type') else 'es'
    es_ins = data.get('es_ins') if data.get('es_ins') else 'ldxkujv6'

    logger.info(f'delete collection: {collection_name}')
    es_delete_collection(collection_name, es_ins)

    return {'code': 10000, 'info': '',
            'data':{}}


@vec_app.post('/edit')
def edit_collection(data: dict = Depends(parse_fastapi_data)):
    collection_name = data.get('collection_name', None)
    user_name = data.get('user_name','tester')
    sid = data.get('session_id', 'UndefinedSID')
    vdb_type = data.get('vdb_type', 'es')
    text = data.get('text', None)
    index_content = data.get('index_content', None)
    es_ins = data.get('es_ins', None)
    lang = data.get('lang', 'zh')

    if not collection_name:
        return {'code': 90001, 
                'info': f'ERROR edit collection: collection name unknown',
                'data':{}}
    if not text:
        return {'code': 90001, 
                'info': f'ERROR edit collection: `text` missing',
                'data':{}}

    if vdb_type != 'es':
        return {'code': 90001, 'info': 'vdb type error: edit support only es'}
    else:
        content = text
        logger.info(f'[{sid}]edit collection: {collection_name}, content: {content}')
        try:
            doc_id, doc_content = update_document_es(user_name, collection_name, content, 
                                es_ins=es_ins,
                                lang=lang,
                                index_content=index_content,
                                doc_name=data.get('doc_name', ''), 
                                threshold=data.get('threshold', 0.6))
            return {'code': 10000, 
                    'info': f'updated {doc_id} original content: {doc_content}',
                    'data':{}}
        except Exception as e:
            logger.error(f'[{sid}]edit collection err: {traceback.format_exc()}')
            return {'code': 90017, 'info': f'update model err:{e}, please retry',
                    'data':{}}



@vec_app.post('/get')
def get_collection(data: dict = Depends(parse_fastapi_data)):
    collection_name = data.get('collection_name', None)
    file_name = data.get('file_name')
    sid = data.get('session_id', 'UndefinedSID')
    vdb_type = data.get('vdb_type') if data.get('vdb_type') else 'es'
    es_ins = data.get('es_ins') if data.get('es_ins') else 'ldxkujv6'
    if not collection_name:
        return {'code': 90001, 'session_id': sid,
                'info': f'ERROR get collection: collection_name unknown'}
    
    try:
        logger.info(f'get collection, collection_name: {collection_name}, file_name: {file_name}')
        from_ = 0
        ret, ids, total_hits = get_documents_es(collection_name, es_ins, file_name,
                                                    size=100, total_hits=True, session_id=sid)
        while(len(ids) < total_hits):
            from_+=100
            _ret, _ids = get_documents_es(collection_name, es_ins, file_name, 
                                        size=100, from_=from_, session_id=sid)
            ret += _ret
            ids += _ids
        logger.info(f'get collection: {ret}')
        return {'code': 10000, 'session_id': sid,
                'info': {'ids':ids},
                'data':{'chunks':ret}}
    except Exception as e:
        logger.error(f'get collection err: {traceback.format_exc()}')
        return {'code': 90018, 'session_id': sid,
                'info': f'get collection err: {e}',
                'data':{}}


@vec_app.post('/search')
def search_collection(data: dict = Depends(parse_fastapi_data)):
    collection_name = data.get('collection_name', None)
    sid = data.get('session_id', '')
    es_ins = data.get('es_ins') if data.get('es_ins') else 'ldxkujv6'
    es_query = data.get('es_query', None)
    question = data.get('text', None)
    lang = data.get('lang', 'zh')
    total_hits = data.get('total_hits', False)
    if not collection_name or not question:
        logger.info(f'search collection lack of collection_name or text: {data}')
    if not collection_name:
        return {'code': 90001, 
                'info': f'search collection err: collection name unknown',
                'data':{}}
    if not question:
        return {'code': 90001, 
                'info': f'search collection err: `text` missing',
                'data':{}}
            
    from_ = data.get('from_', 0)
    if from_:
        try:
            from_ = int(from_)
        except:
            return {'code': 90001, 
                    'info': f'ERROR search: from_ must be an integer'}
    top_n = data.get('size') or data.get('top_n')
    if top_n:
        try:
            top_n = int(top_n)
        except:
            return {'code': 90001, 
                    'info': f'ERROR search: top_n must be an integer'}
    threshold = data.get('threshold', 0)
    if threshold:
        try:
            threshold = float(threshold)
        except:
            return {'code': 90001, 
                    'info': f'ERROR search: threshold must be a float'}
    text_weight = data.get('text_weight', 0)
    if text_weight:
        try:
            text_weight = float(text_weight)
        except:
            return {'code': 90001, 
                    'info': f'ERROR search: text_weight must be a float'}
    if es_query:
        try:
            es_query = json.loads(es_query.replace('raw_str', question))
        except:
            return {'code': 90001, 
                    'info': f'ERROR search: es_query must be a json string with `raw_str` keyword'}
    logger.info(f'[{sid}]search collection: {data}')
    text_recall_top_n = data.get('text_recall_top_n', 0)
    if text_recall_top_n:
        try:
            text_recall_top_n = int(text_recall_top_n)
        except:
            return {'code': 90001, 
                    'info': f'ERROR search: text_recall_top_n must be an integer'}
    if text_recall_top_n > 0 and not top_n:
        top_n = text_recall_top_n
        text_weight = 1
    top_n = top_n or 10
    try:
        info = ''
        ret = search_documents_es(collection_name, question, 
                                    top_n=top_n, threshold=threshold,
                                    query_dict=es_query,
                                    from_=from_, lang=lang, es_ins=es_ins, 
                                    total_hits=total_hits, text_weight=text_weight, session_id = sid)
        if total_hits:
            ret, n_hits = ret
            info = {'total_hits': n_hits}
        logger.info(f'[{sid}]search collection result:{ret}')
        return {'code': 10000, 'session_id': sid,
                'info': info,
                'data':{'chunks':ret}}
    except Exception as e:
        logger.error(f'[{sid}]search collection err:{traceback.format_exc()}')
        return {'code': 90010,  'session_id': sid,
                'info': f'search collection err: {e}',
                'data':{}}


@vec_app.post('/get_chunks_byid')
def get_chunks_byid(data: dict = Depends(parse_fastapi_data)):
    if not data:
        return {'code': 90001, 
                'info': 'Failed to parse request data',
                'data':{'code':90001,
                        'info':'Failed to parse request data'}}
    print('===',data)
    collection_name = data.get('collection_name')
    ids = data.get('ids')
    sid = data.get('session_id', 'UndefinedSID')
    es_ins = data.get('es_ins') if data.get('es_ins') else 'ldxkujv6'
    if not collection_name or not ids:
        logger.info(f'get chunks byid lack of collection_name or ids')
    logger.info(f'get chunks byid: {data}')
    try:
        ret = get_documents_byid_es(collection_name, es_ins, ids)
        logger.info(f'get chunks byid result:{ret}')
        return {'code': 10000, 'session_id': sid,
                'info': '',
                'data':{'chunks':ret}}
    except Exception as e:
        logger.error(f'get chunks byid err:{traceback.format_exc()}')
        return {'code': 90013,  'session_id': sid,
                'info': f'search collection err: {e}',
                'data':{'code':90013,
                        'session_id':sid,
                        'info':f'search collection err: {e}'}}


@vec_app.post('/update_chunks_byid')
def update_chunks_byid(data: dict = Depends(parse_fastapi_data)):
    '''chunks:{ 'id': doc_id,
                  'content': ans}'''
    collection_name = data.get('collection_name', None)
    sid = data.get('session_id', 'UndefinedSID')
    es_ins = data.get('es_ins') if data.get('es_ins') else 'ldxkujv6'
    chunks = data.get('chunks', None)
    lang = data.get('lang', 'zh')
    if not collection_name  or not chunks:
        logger.info(f'update chunks byid lack of collection_name or ids or contents')
    logger.info(f'update chunks byid: {data}')
    try:
        ret = update_documents_byid_es(collection_name, es_ins, lang, chunks)
        logger.info(f'update chunks byid result:{ret}')
        return {'code': 10000, 'session_id': sid,
                'info': '',
                'data':{}}
    except Exception as e:
        logger.error(f'update chunks byid err:{traceback.format_exc()}')
        return {'code': 90015,  'session_id': sid,
                'info': f'update chunks byid err: {e}',
                'data':{}}


@vec_app.post('/delete_chunks_byid')
def delete_ids(data: dict = Depends(parse_fastapi_data)):
    collection_name = data.get('collection_name', None)
    doc_ids = data.get('vec_ids', [])
    es_ins = data.get('es_ins', None)
    if not collection_name or not doc_ids:
        return {'code': 90001, 
                'info': f'ERROR delete vec: collection name or doc_ids unknown'}
    try:
        if data.get('vdb_type','es')!='es':
            return {'code': 90051, 'info': 'vdb type error: es only',
                    'data':{}}
        ret = delete_documents_es(collection_name, es_ins, doc_ids)
        return {'code': 10000, 'info': '', 'data': ret}
    except Exception as e:
        logger.error(f'delete vec err:{traceback.format_exc()}')
        return {'code': 90010, 'info': f'error search documents: {e}',
                'data':{}}


@vec_app.post('/upsert_progress')
def collection_upsert_progress(data: dict = Depends(parse_fastapi_data)):
    sid = data.get('session_id', None)
    if not sid or not connection_pool:
        return {'code':90009,'info':'no session_id provided or no db conn',
                'data':{}}
    
    # cursor = sql_progress_conn.cursor(dictionary=True)
    # cursor.execute(f"SELECT * FROM {SQL_PROGRESS_TABLE} WHERE sid = '{sid}'")
    # row = cursor.fetchone()

    try:
        sql_progress_conn = connection_pool.get_connection()
        cursor = sql_progress_conn.cursor()
        cursor.execute(f"SELECT * FROM {SQL_PROGRESS_TABLE} WHERE sid = '{sid}'")
        row = cursor.fetchone()
    except:
        logger.error(f'collection upsert progress, sql pool err:{traceback.format_exc()}')
        return {'code':90009,'info':'no session_id provided or no db conn',
                'data':{}}

    # if fetched, turn JSON
    if row:
        columns = [desc[0] for desc in cursor.description]  # 获取列名
        row = dict(zip(columns, row))
        if 'update_time' in row:
            row['update_time'] = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(
                time.mktime(row['update_time'].timetuple())))
        if 'create_time' in row:
            row['create_time'] = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(
                time.mktime(row['create_time'].timetuple())))
        row['code'] = 10000
    else:
        row = {'code':10000,'info':'no result'}
    if cursor:
        cursor.close()
    if sql_progress_conn:
        sql_progress_conn.close()
    data = row
    return data


@vec_app.post('/text_vec_sim')
def t2vec_sim(data: dict = Depends(parse_fastapi_data)):
    try:
        text1 = data.get('text1')
        text2 = data.get('text2')
        lang = data.get('lang', 'zh')
        sim = calc_text_sim(text1, text2, lang)
    except Exception as e:
        logger.error(f'text vec sim err:{traceback.format_exc()}')
        return {'code': 90016, 'info': f'text vec sim err: {e}',
                'data':{}}
    return {'code': 10000, 'info': '', 'data': sim}




if __name__ == '__main__':
    with open('/data/dat/rag/split_cpu_overhead_case.txt','r') as f:
        text = f.read()
    import time
    t0=time.time()
    r,_=split_texts_for_collection({'texts':[('doc.txt',text)],'lang':'zh'})
    print('===splitted',len(r[0]),time.time()-t0)



