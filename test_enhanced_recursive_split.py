#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 enhanced_recursive_split 函数的性能和内存问题
"""

import time
import tracemalloc
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.text_processing import enhanced_recursive_split


def test_infinite_recursion():
    """测试无限递归问题"""
    print("=== 测试无限递归问题 ===")
    
    # 创建一个没有任何分隔符的长文本
    # text = "a" * 1000  # 10000个字符，没有分隔符
    text = '\n$$ P=\\frac{D-M}{M}\\times 100\\%\\qquad\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\cdots\\c'
    
    try:
        start_time = time.time()
        result = enhanced_recursive_split(
            text, 
            separators=["\n", ".", ","],  # 这些分隔符在文本中都不存在
            chunk_size=100,
            chunk_overlap=10
        )
        end_time = time.time()
        print(f"✓ 无限递归测试通过，耗时: {end_time - start_time:.2f}秒")
        print(f"  结果块数: {len(result)}")
        print(f"  第一个块长度: {len(result[0]) if result else 0}")
    except RecursionError as e:
        print(f"✗ 发生递归错误: {e}")
    except Exception as e:
        print(f"✗ 发生其他错误: {e}")


def test_memory_usage_with_duplicates():
    """测试重复内容导致的内存问题"""
    print("\n=== 测试重复内容的内存使用 ===")
    
    # 创建包含大量重复内容的文本
    base_text = "这是一段重复的文本内容。" * 100  # 基础重复文本
    text = base_text + "\n" + base_text + "\n" + base_text  # 创建重复段落
    
    tracemalloc.start()
    start_time = time.time()
    
    try:
        result = enhanced_recursive_split(
            text,
            chunk_size=500,
            chunk_overlap=50,
            chunk_min=100
        )
        
        end_time = time.time()
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        print(f"✓ 重复内容测试完成")
        print(f"  耗时: {end_time - start_time:.2f}秒")
        print(f"  当前内存使用: {current / 1024 / 1024:.2f} MB")
        print(f"  峰值内存使用: {peak / 1024 / 1024:.2f} MB")
        print(f"  结果块数: {len(result)}")
        
        # 检查是否有重复内容
        total_length = sum(len(chunk) for chunk in result)
        print(f"  原文本长度: {len(text)}")
        print(f"  结果总长度: {total_length}")
        print(f"  长度比例: {total_length / len(text):.2f}")
        
    except Exception as e:
        tracemalloc.stop()
        print(f"✗ 发生错误: {e}")


def test_infinite_loop_in_overlap():
    """测试重叠检测中的无限循环"""
    print("\n=== 测试重叠检测的无限循环 ===")
    
    # 创建特殊的文本，可能触发第129-131行的无限循环
    text = "aaaaaaaaaa" * 1000  # 大量重复字符
    
    start_time = time.time()
    try:
        result = enhanced_recursive_split(
            text,
            chunk_size=100,
            chunk_overlap=50
        )
        end_time = time.time()
        print(f"✓ 重叠检测测试通过，耗时: {end_time - start_time:.2f}秒")
        print(f"  结果块数: {len(result)}")
        
        # 检查是否有异常长的处理时间（可能表示循环问题）
        if end_time - start_time > 5:
            print(f"⚠ 警告: 处理时间过长，可能存在性能问题")
            
    except Exception as e:
        print(f"✗ 发生错误: {e}")


def test_large_text_performance():
    """测试大文本的性能"""
    print("\n=== 测试大文本性能 ===")
    
    # 创建大文本
    text = "这是一段测试文本。" * 10000  # 约20万字符
    
    tracemalloc.start()
    start_time = time.time()
    
    try:
        result = enhanced_recursive_split(
            text,
            chunk_size=1000,
            chunk_overlap=100,
            chunk_min=500
        )
        
        end_time = time.time()
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        print(f"✓ 大文本测试完成")
        print(f"  原文本长度: {len(text):,} 字符")
        print(f"  耗时: {end_time - start_time:.2f}秒")
        print(f"  峰值内存使用: {peak / 1024 / 1024:.2f} MB")
        print(f"  结果块数: {len(result)}")
        print(f"  平均每块长度: {sum(len(chunk) for chunk in result) / len(result):.0f}")
        
        # 性能警告
        if end_time - start_time > 10:
            print(f"⚠ 警告: 处理时间过长 ({end_time - start_time:.2f}秒)")
        if peak / 1024 / 1024 > 100:
            print(f"⚠ 警告: 内存使用过高 ({peak / 1024 / 1024:.2f} MB)")
            
    except Exception as e:
        tracemalloc.stop()
        print(f"✗ 发生错误: {e}")


def test_pathological_case():
    """测试病理情况：大量重复分隔符"""
    print("\n=== 测试病理情况：大量重复分隔符 ===")
    
    # 创建包含大量重复分隔符的文本
    text = "文本。" * 1000 + "。" * 1000 + "文本。" * 1000
    
    start_time = time.time()
    try:
        result = enhanced_recursive_split(
            text,
            separators=["。", "，", "\n"],
            chunk_size=200,
            chunk_overlap=20,
            chunk_min=50
        )
        end_time = time.time()
        
        print(f"✓ 病理情况测试完成，耗时: {end_time - start_time:.2f}秒")
        print(f"  结果块数: {len(result)}")
        
        # 检查结果的合理性
        for i, chunk in enumerate(result[:5]):  # 只检查前5个
            print(f"  块{i+1}长度: {len(chunk)}")
            
    except Exception as e:
        print(f"✗ 发生错误: {e}")


if __name__ == "__main__":
    print("开始测试 enhanced_recursive_split 函数...")
    
    test_infinite_recursion()
    test_memory_usage_with_duplicates()
    test_infinite_loop_in_overlap()
    test_large_text_performance()
    test_pathological_case()
    
    print("\n测试完成！")
