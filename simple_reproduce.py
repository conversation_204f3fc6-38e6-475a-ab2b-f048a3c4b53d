#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简单的复现例子：直接调用 enhanced_recursive_split 函数
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.text_processing import enhanced_recursive_split



def enhanced_recursive_split_v3(
        text: str,
        separators: Optional[List[str]] = None,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        chunk_min: Optional[int] = None,
) -> List[str]:
    """
    增强版递归字符分割器
    :param text: 待分割文本
    :param chunk_size: 块最大字符数（建议500-2000）
    :param chunk_overlap: 块间重叠字符数（建议10%-20% chunk_size）
    :param separators: 优先级递减的分割符列表
    :param min_length: 最小块长度（强制合并小块的阈值）
    :return: 分割后的文本块列表
    """
    # 参数有效性检查
    if chunk_size <= 0:
        raise ValueError(f"chunk_size must be positive, got {chunk_size}")
    if chunk_overlap < 0:
        raise ValueError(f"chunk_overlap must be non-negative, got {chunk_overlap}")
    if chunk_overlap >= chunk_size:
        raise ValueError(f"chunk_overlap ({chunk_overlap}) must be smaller than chunk_size ({chunk_size})")

    # 默认中文友好分隔符
    default_separators = ["\n\n", "\n", "。", "？", "！", "；", "，", "."]
    separators = separators or default_separators

    def split_with_separator(text: str, sep: str) -> List[str]:
        """使用分隔符分割文本，并保留分隔符"""
        if not sep:
            return [text]

        # 使用正则表达式来分割，保留所有分隔符
        pattern = f"({re.escape(sep)}+)"
        parts = re.split(pattern, text)

        # 重新组合部分，确保分隔符被正确保留
        result = []
        for i in range(0, len(parts) - 1, 2):
            if i + 1 < len(parts):
                result.append(parts[i] + parts[i + 1])
            else:
                result.append(parts[i])

        # 处理最后一个部分
        if len(parts) % 2 == 1:
            result.append(parts[-1])

        return result

    def process_chunk(chunk: str, current_seps: List[str], depth: int = 0) -> List[str]:
        if len(chunk) <= chunk_size:
            return [chunk]

        if not current_seps:
            result = []
            start = 0
            last_end = 0
            max_iterations = 15000
            iteration_count = 0

            while start < len(chunk):
                iteration_count += 1
                if iteration_count > max_iterations:
                    result.append(chunk[start:])
                    print("警告：达到最大迭代次数，强制终止分割")
                    break

                end = min(start + chunk_size, len(chunk))
                if start >= end:
                    break

                # ======== 关键优化：处理连续重复内容 ========
                # 检查当前切分位置是否在连续重复区域内
                if end < len(chunk):
                    # 检测当前块结尾处的连续重复模式
                    repeat_char = chunk[end-1]
                    repeat_start = end - 1
                    # 向前找到重复区域的真实起点
                    while repeat_start > start and chunk[repeat_start-1] == repeat_char:
                        repeat_start -= 1

                    # 向后检查重复区域的长度
                    repeat_end = end - 1
                    while repeat_end < len(chunk) - 1 and chunk[repeat_end+1] == repeat_char:
                        repeat_end += 1

                    repeat_length = repeat_end - repeat_start + 1

                    # 如果处于长重复区域（超过chunk_size/2）
                    if repeat_length >= max(10, chunk_size // 2):
                        # 策略1：尝试跳过整个重复区域
                        next_boundary = repeat_end + 1

                        # 策略2：若剩余空间不足，则直接包含部分重复区
                        if next_boundary - start > chunk_size * 1.5:
                            # 将切分点设为重复区起点
                            end = repeat_start
                            new_start = max(
                                min(last_end + 1, start + 1),
                                min(repeat_end + 1 - chunk_overlap, end)
                            )
                        else:
                            # 包含部分重复区再跳过
                            end = min(repeat_end + 1, start + chunk_size)
                            new_start = max(
                                min(last_end + 1, start + 1),
                                end - chunk_overlap
                            )

                        # 添加调整后的块
                        result.append(chunk[start:end])
                        last_end = end
                        start = new_start
                        continue

                # ======== 常规切分逻辑 ========
                result.append(chunk[start:end])

                if end < len(chunk):
                    min_advance = max(last_end + 1, start + 1)
                    new_start = max(min_advance, end - chunk_overlap)

                    # 防止小步长前进：确保至少移动10%的chunk_size
                    min_move = max(1, chunk_size // 10)
                    if new_start - start < min_move:
                        new_start = min(start + min_move, len(chunk))

                    last_end = end
                    start = new_start
                else:
                    start = end

            return result

        # 使用当前分隔符分割
        current_sep = current_seps[0]
        parts = split_with_separator(chunk, current_sep)

        # 处理每个部分
        result = []
        current = ""
        for part in parts:
            if len(current) + len(part) <= chunk_size:
                current += part
            else:
                if current:
                    result.append(current)
                # 对过长的部分使用下一级分隔符递归处理
                result.extend(process_chunk(part, current_seps[1:], depth + 1))
                current = ""

        if current:
            result.append(current)

        return result

    # 执行分割
    chunks = process_chunk(text, separators)

    # 处理最小长度要求（保持不变）
    if chunk_min is not None:
        merged = []
        current = ""

        for chunk in chunks:
            # 检查当前块是否以分隔符结尾
            ends_with_sep = any(chunk.endswith(sep) for sep in separators)
            # 检查下一个块是否以分隔符开头
            next_starts_with_sep = any(chunk.startswith(sep) for sep in separators)

            # 如果当前块小于chunk_min，尝试合并
            if len(current) < chunk_min:
                # 如果当前块以分隔符结尾，或者下一个块以分隔符开头，直接合并
                if ends_with_sep or next_starts_with_sep:
                    current += chunk
                else:
                    # 检查是否有重复内容
                    if current and chunk:
                        # 找到最长的公共后缀和前缀
                        overlap = 0
                        for i in range(1, min(len(current), len(chunk)) + 1):
                            if current[-i:] == chunk[:i]:
                                overlap = i
                        if overlap > 0:
                            current += chunk[overlap:]
                        else:
                            current += chunk
                    else:
                        current += chunk
            # 如果当前块已经达到chunk_min，且合并后不超过chunk_size，则合并
            elif len(current) + len(chunk) <= chunk_size:
                current += chunk
            else:
                if current:
                    merged.append(current)
                current = chunk

        # 处理最后一个块
        if current:
            # 如果最后一个块小于chunk_min，尝试与前面的块合并
            if len(current) < chunk_min and len(merged) > 0:
                # 检查最后一个合并块是否以分隔符结尾
                last_ends_with_sep = any(merged[-1].endswith(sep) for sep in separators)
                # 检查当前块是否以分隔符开头
                current_starts_with_sep = any(current.startswith(sep) for sep in separators)

                if last_ends_with_sep or current_starts_with_sep:
                    merged[-1] += current
                else:
                    # 检查是否有重复内容
                    overlap = 0
                    for i in range(1, min(len(merged[-1]), len(current)) + 1):
                        if merged[-1][-i:] == current[:i]:
                            overlap = i
                    if overlap > 0:
                        merged[-1] += current[overlap:]
                    else:
                        merged[-1] += current
            else:
                merged.append(current)

        # 最后检查是否还有小于chunk_min的块
        final_merged = []
        current = ""
        for chunk in merged:
            if len(chunk) < chunk_min:
                if current:
                    current += chunk
                else:
                    current = chunk
            else:
                if current:
                    final_merged.append(current)
                final_merged.append(chunk)
                current = ""

        if current:
            final_merged.append(current)

        chunks = final_merged

    return chunks



# 最简单的复现用例 - 会导致无限循环
print("开始测试...")
print("调用: enhanced_recursive_split('aaaaaa', chunk_size=4, chunk_overlap=2)")
print("预期结果: 程序会卡住（无限循环）")
print()

result = enhanced_recursive_split_v3(
    text="aaaaaa" * 6,         
    chunk_size=10,            
    chunk_overlap=2          
)

print("结果:", result)