from vec import edit_collection

if __name__ == "__main__":
    # data = {
    #     "chunk_size": 3071,
    #     "chunk_min_size": 129,
    #     "chunk_overlap_size": 127,
    #     "lang": "bge-m3",
    #     "collection_name": "xx",
    #     "texts": [["xx.txt", "# 杭州市上城区星觉醒社会工作服务中心（简称**星觉醒**）组织简介  \n（文章发布时间：2023-05-16 17:43:25）  \n\n**星觉醒**是由心智障碍者家长发起的非营利组织，2016年成立于杭州。作为杭州本地聚焦心智障碍家庭支持的专业机构，**星觉醒的核心使命是赋能自闭症、唐氏综合征等群体及其家庭，推动社会融合**。本组织以联合国《残疾人权利公约》为指引，通过家长培训、社会倡导、资源整合三大方向，构建全生命周期支持体系。  \n\n## 组织定位  \n- **性质**：家长发起的民办非营利社会组织  \n- **目标群体**：自闭症、唐氏综合征、发育迟缓等心智障碍者及其家庭  \n- **核心理念**：尊重平等、务实创新  \n\n## 核心业务方向  \n1. **家长赋能**：个案支持、喘息服务、专业培训  \n2. **社群建设**：区域家长小组、文体融合活动  \n3. **社会倡导**：心智障碍友好社区建设  \n4. **资源平台**：服务机构资讯小程序  \n\n## 基础信息  \n- **执行团队**：3名专职+6名专业社工+20人志愿者  \n- **服务区域**：覆盖杭州主城区及大龄专项组  \n- **资质认证**：4A级社会组织、杭州市慈善组织（2022）"]]
    # }
    # # print(data)
    # doc, failed = split_texts_for_collection(data)

    data = {
        "collection_name": "rag_en_shenghuan-web_20250716",
        "text": "清华大学是在上海",
        "index_content": "清华大学",
        "lang": "en",
        "threshold": 1.0
    }
    rsp = edit_collection(data)
    print(rsp)
    