#FROM csighub.tencentyun.com/algo-services/base-tlinux2.2-python3.9-cpu AS serv-env
#FROM csighub.tencentyun.com/ssv_ml/tlinux-python311:v1 AS serv-env
FROM python:3.11.11-bullseye AS serv-env

ENV LANG zh_CN.UTF-8
ENV LC_CTYPE zh_CN.UTF-8
#ADD  data /app/data
COPY __init__.py gpt3.py llms.py config.py vec.py uvi_deploy.py vectordb4es.py uvi_requirements.txt ssv_logger.py load_balancer.py /app/
COPY /utils /app/utils
WORKDIR /app

#RUN curl http://openapi.zhiyan.woa.com/zhiyan_agent_install_shell/upgrade_zhiyan_agent.sh |xargs -0 -i sh -c {} -a log
RUN mkdir /var/log/serv && \
    chmod 777 /app
# RUN mkdir -p /root/nltk_data
# RUN tar -xf ./nltk_data.tar && cp -r ./nltk_data/* /root/nltk_data/ && rm -r ./nltk_data

# RUN apt-get install python-dev libxml2-dev libxslt1-dev antiword unrtf poppler-utils
RUN pip3 install -r uvi_requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ 
# RUN pip3 install pdfplumber==0.11.6 -i https://mirrors.tencent.com/pypi/simple/

EXPOSE 8001

ENTRYPOINT ["python3", "uvi_deploy.py"]
