# R-RAG-Dev: 智能检索增强生成系统

一个基于FastAPI的高性能检索增强生成(RAG)系统，支持多模型负载均衡、文档检索、向量数据库管理和智能问答。

## 🚀 主要特性

- **多模型支持**: 支持混元、DeepSeek、Gemini等多种大语言模型
- **智能负载均衡**: 自动故障转移和并发控制
- **向量检索**: 基于Elasticsearch的高效文档检索
- **流式响应**: 支持实时流式对话
- **文档管理**: 完整的文档上传、分块、索引管理
- **智能分类**: 自动识别问题类型(聊天/简单/复杂)
- **多语言支持**: 支持中文、英文等多种语言

## 📁 项目结构

```
r-rag-dev/
├── gpt3.py              # 主要API服务，处理聊天和问答
├── vec.py               # 向量数据库管理服务
├── uvi_deploy.py        # 服务部署入口
├── config.py            # 配置文件
├── llms.py              # 大语言模型接口和负载均衡
├── utils/               # 工具模块
│   ├── query_parser.py  # 请求解析
│   ├── query_nlp.py     # 查询分类和NLP处理
│   ├── text_loader.py   # 文件加载和内容抽取
│   └── text_processing.py # 文本处理
└── README.md           # 项目文档
```

## 🛠️ 技术栈

- **后端框架**: FastAPI
- **向量数据库**: Elasticsearch
- **负载均衡**: 自定义负载均衡器
- **模型接口**: OpenAI兼容API
- **异步处理**: asyncio + httpx
- **重试机制**: tenacity

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -f uvi_requirements.txt
或直接
uv sync

# 配置环境变量
export SQL_HOST=your_mysql_host
export SQL_DB=your_database
export SQL_U=your_username
export SQL_PW=your_password
```

### 2. 启动服务

```bash
# 启动完整服务
python uvi_deploy.py

# 或分别启动
python gpt3.py  # 启动聊天服务
python vec.py   # 启动向量服务
```

服务将在 `http://localhost:8001` 启动

## 📚 API 接口

### 聊天和问答接口

#### 1. 流式聊天 `/v1/chat_stream`

```python
POST /v1/chat_stream
{
    "text": "你好，请介绍一下人工智能",
    "model": "hunyuan-standard",
    "collection_name": "ai_docs",
    "stream": true,
    "search": true,
    "session_id": "user123"
}
```

#### 2. 普通聊天 `/v1/chat`

```python
POST /v1/chat
{
    "messages": [
        {"role": "user", "content": "什么是机器学习？"}
    ],
    "model": "hunyuan-standard",
    "collection_name": "ml_docs"
}
```


### 向量数据库管理接口

#### 1. 创建集合 `/collection/create`

```python
POST /collection/create
{
    "collection_name": "my_docs",
    "texts": [
        ["doc1.txt", "这是第一个文档的内容..."],
        ["doc2.txt", "这是第二个文档的内容..."]
    ],
    "chunk_size": 320,
    "lang": "zh"
}
```

#### 2. 搜索文档 `/collection/search`

```python
POST /collection/search
{
    "collection_name": "my_docs",
    "text": "查询关键词",
    "size": 5,
    "threshold": 0.6
}
```
##### [INH:xxx] 类型重定向召回说明

在文档检索时，如果某个文档的内容为 `[INH:xxx]`（如 `[INH:parent_doc_id]`），表示该文档内容继承自 `parent_doc_id` 对应的文档。系统会自动查找 `parent_doc_id` 的实际内容，并将其作为当前文档的内容返回。这种机制常用于文档继承、版本管理或内容复用场景。

**示例：**

假设有如下文档：

- 文档A（id: `docA`）：内容为 `"这是原始文档内容"`
- 文档B（id: `docB`）：内容为 `[INH:docA]`

当检索到文档B时，系统会自动返回文档A的内容，即 `"这是原始文档内容"`，而不是 `[INH:docA]`。

**注意事项：**
- 仅当文档内容严格为 `[INH:xxx]` 格式时才会触发重定向召回。
- 如果父文档不存在或被删除，则返回空或报错。
- 该机制在 `/collection/search` 等检索接口中自动生效，无需额外参数。



#### 3. 获取文档 `/collection/get`

```python
POST /collection/get
{
    "collection_name": "my_docs",
    "file_name": "doc1.txt"
}
```

#### 4. 删除集合 `/collection/delete`

```python
POST /collection/delete
{
    "collection_name": "my_docs"
}
```

#### 5. 插入chunk的分身向量 `/collection/add_segments`

```python
// INSERT_YOUR_CODE

POST /collection/add_segments
{
    "collection_name": "my_docs",
    "chunk_content": "第一句话。第二句话。第三句话。",
    "chunk_id": "doc1_chunk1",
    "doc_name": "doc1.txt",
    "threshold": 0.6,
    "lang": "zh"
}

// 说明：
// - `chunk_content` 为需要分割的长文本，系统会自动按句子分割并计算每句的embedding。
// - `threshold` 控制分组的相似度阈值，距离大于该值的句子会被分到不同的分身组。
// - 返回值为插入结果，若分组数大于1，则每组会生成一个新的分身向量，内容为 [INH:chunk_id]。

```


## 🔧 配置说明

### 模型配置

在 `config.py` 中配置支持的模型：

```python
REMOTE_MODELS = {
    'hunyuan': {
        'url': 'http://your-model-server/llm/hunyuan',
        'model_name': 'hunyuan',
        'key': 'your-api-key'
    },
    'deepseek-r1': {
        'url': 'https://api.lkeap.cloud.tencent.com/v1',
        'key': 'your-api-key'
    }
}
```

### 数据库配置

```python
SQL_HOST = 'your-mysql-host'
SQL_DB = 'your-database'
SQL_PROGRESS_TABLE = 'rag_progress'
SQL_U = 'your-username'
SQL_PW = 'your-password'
```

### Elasticsearch配置

```python
ES_CONFIGS = {
    "default": {
        "ip": "your-es-host",
        "port": "9200",
        "user": "elastic",
        "password": "your-password"
    }
}
```

## 🎯 核心功能

### 1. 智能问题分类

系统自动识别用户输入类型：
- **聊天**: 日常对话，使用轻量级模型
- **简单问题**: 事实性查询，使用标准模型
- **复杂问题**: 需要深度思考，使用高性能模型

### 2. 多轮对话处理

支持上下文理解和多轮对话：
- 自动总结多轮对话内容
- 智能提取关键查询词
- 保持对话连贯性

### 3. 文档检索增强

- **语义检索**: 基于向量相似度
- **关键词检索**: 基于文本匹配
- **混合检索**: 结合语义和关键词
- **相关性过滤**: 自动过滤不相关内容

### 4. 负载均衡

- **多环境支持**: 支持多个模型服务环境
- **自动故障转移**: 服务异常时自动切换
- **并发控制**: 限制每个模型的并发请求数
- **优先级管理**: 按优先级选择服务

## 📊 性能优化

### 1. 异步处理

- 使用 `asyncio` 实现高并发
- 支持流式响应，提升用户体验
- 异步数据库操作

### 2. 缓存机制

- Redis缓存热点数据
- 向量计算结果缓存
- 会话状态缓存

### 3. 批量处理

- 文档批量上传
- 向量批量计算
- 数据库批量操作

## 🔍 监控和日志

### 日志配置

```python
from ssv_logger import ssv_logger
ssv_logger('app.log')
```

### 进度跟踪

支持长时间任务的进度跟踪：
- 文档上传进度
- 向量计算进度
- 错误状态记录

## 🚨 错误处理

### 常见错误码

- `10000`: 成功
- `90001`: 参数错误
- `90010`: 检索失败
- `90012`: 创建集合失败
- `90020`: 模型请求失败

### 重试机制

使用 `tenacity` 库实现自动重试：
- 网络请求重试
- 数据库操作重试
- 模型调用重试


---

**注意**: 使用前请确保已正确配置所有必要的API密钥和数据库连接信息。
