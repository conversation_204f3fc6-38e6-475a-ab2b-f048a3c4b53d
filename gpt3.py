#encoding=utf8
import json
import logging
import random
import time
import os

from concurrent.futures import ThreadPoolExecutor
from ssv_logger import ssv_logger
from utils.model_logger import log_model_call_async, log_model_call
ssv_logger('gpt3.log')
logger = logging.getLogger(__name__)

import numpy as np
from fastapi import FastAPI, Depends, HTTPException
from fastapi.responses import StreamingResponse
import httpx
import asyncio
import traceback
import openai

from config import GATE_URL_LOCAL,\
                SEARCH_URL,REMOTE_MODELS,OPENAI_MODELS,FALLBACK_ENGINES, REASONING_EFFORT_MODELS, LOAD_BALANCE_CONFIG

from utils.query_parser import parse_fastapi_data
from llms import request_stream_with_load_balance,request_with_load_balance
from utils.query_nlp import DictQueryClassifier
import base64
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from typing import Tuple, List

# app = Flask(__name__)
gpt_app = FastAPI(debug=True,docs_url=None,redoc_url=None)



dr_prompt = '你是一个专家助手，你的任务是阅读后面提供的参考材料，从中选择对应的内容回答问题，问题如下：{} \n 参考材料如下：\n'

REWRITE_PROMPT_SEARCH = 'Given the question: \n{}\n please extract'+\
            'keywords that can be used to search for an answer to this question'+\
            ' using a search engine, separated with space. Ignore irrelevant callings.'

REWRITE_PROMPT_MUL='以下为用户的连续问题（或表达），根据前面的问题（或表达）改写最后一个使其更完整方便理解，'+\
        '如前后不相关则直接输出最后一个原问题（或表达）\n用户连续对话输入: {}\n最后一个输入(的改写结果)为:'

JUDGE_PROMPT_RELEVANCE='判断后面检索内容与用户query是否相关，仅回答`是`或`否`,\n用户query为:{}\n检索内容为:\n'

CHECK_PROMPT_TYPE='判断用户输入为聊天、简单问题、还是需要思考的复杂问题，仅回答`聊天`,`简单问题`或'+\
        '`复杂问题`(需要深度逻辑思考的问题)\n用户输入为:{}\n用户输入类型为:'

async def _model_request_wrapper(data, model, timeout=None):
    response = await request_with_load_balance(data, timeout)
    if isinstance(response, httpx.Response):
        response = response.json()
    if isinstance(response, openai.types.chat.chat_completion.ChatCompletion):
        response = response.dict()
    if not isinstance(response, dict):
        raise ValueError(f'model ret is not a dict: {response}')
    if 'choices' not in response:
        try:
            response = json.loads(response['_content'])
        except:
            raise ValueError(f'model ret is not in _content: {response}')
    return response

@log_model_call_async(key='search_query_rewrite', model='hunyuan', extract_keys=['sid','query'])
async def _search_query_rewrite(query, model='', sid=''):
    model = 'hunyuan'
    data = {
        'model': model,
        'messages': [
            {'role': 'user', 'content': REWRITE_PROMPT_SEARCH.format(query)}
        ],
        'session_id': sid
    }
    try:
        response = await _model_request_wrapper(data, model, timeout=LOAD_BALANCE_CONFIG.get('search_query_rewrite_timeout', 10))
        ret = response['choices'][0]['message']['content'].strip()
        if ret:
            logger.info(f'[{sid}]return from search query rewrite: {ret}')
            return ret
    except Exception as e:
        logger.info(f'[{sid}]search query rewrite failed, use raw query: {query}, {e}')
        return query

@log_model_call_async(key='use_multiround_summary', model='hunyuan', extract_keys=['sid','query'])
async def _use_multiround_summary(query, model='', sid=''):
    # 输入list of dict，输出文本
    # ALL IN HUNYUAN before all-mighty prompt-templete
    model = 'hunyuan'
    query_q = ''
    for s_i in range(len(query)):
        if query[s_i]['role'] == 'user':
            query_q+= ' '+str(s_i//2+1)+'.'+query[s_i]['content']

    data= {'messages':[{'role': 'user', 'content': REWRITE_PROMPT_MUL.format(query_q)}],
            'model':model, 'security':False, 'session_id': sid}
    try:
        response = await _model_request_wrapper(data, model, timeout=LOAD_BALANCE_CONFIG.get('use_multiround_summary_timeout', 10))
        ret = response['choices'][0]['message']['content'].strip()
        if ret:
            logger.info(f'[{sid}]return from multiround query rewrite: {ret}')
            return ret
    except Exception as e:
        ret = query[-1]['content']
        logger.info(f'[{sid}]multiround query rewrite failed, use last content raw: '
                    f'{ret}, {traceback.format_exc()}')
        return ret


async def _user_time_rewrite(query):
    return f'Now is {time.strftime("%Y-%m-%d %H:%M:%S")}, {query}'


async def _judge_prompt_type(query, model='', sid=''):
    # 判断用户输入的类型
    # 输入：list of dict
    # 输出：str, 'chat', 'simple', 'complex'
    model = 'gemma3:27b'
    data = {'messages': [{'role': 'user', 'content': CHECK_PROMPT_TYPE.format(query)}],
             'model': model, 'security': False, 'session_id': sid}
    ret = None
    try:
        response = await _model_request_wrapper(data, model, timeout=LOAD_BALANCE_CONFIG.get('judge_prompt_type_timeout', 10))
        ret = response['choices'][0]['message']['content'].strip()
        if ret:
            logger.info(f'[{sid}]return from judge prompt type: {ret}')
            if ret == '复杂问题':
                return 'complex'
            elif ret == '简单问题':
                return 'simple'
            elif ret == '聊天':
                return 'chat'
        return None
    except Exception as e:
        logger.error(f'[{sid}]judge prompt type failed, {e}')
    

@log_model_call(key='judge_prompt_type_local', extract_keys=['query'])
def _judge_prompt_type_local(query):
    dqc = DictQueryClassifier()
    time_query = dqc.is_time_query(query)
    if dqc.is_chat_query(query):
        type_query = 'chat'
    elif dqc.is_complex_question(query):
        type_query = 'complex'
    else:
        type_query = 'simple'
    prompt_types = {'type':type_query, 'time':time_query}
    return prompt_types


@log_model_call_async(key='clean_chunks', model='hunyuan-lite', extract_keys=['sid','question','chunks'])
async def clean_chunks(question, chunks, sid):
    '''
    输出数据结构：
    [
        {
            'id': str,
            'content': str,
            'raw_content': str,
            'doc_name': str,
        }
    ]
    '''
    async def check_chunk_with_gemma(chunk):
        model = 'hunyuan-lite'
        chunk['raw_content'] = chunk['content']
        prune_chunk=chunk['content'].replace('\n',' ').replace('、','.')
        this_prompt = JUDGE_PROMPT_RELEVANCE.format(question)+'\n'+prune_chunk
        data = {'messages': [{'role': 'user', 'content': this_prompt}],
             'model': model, 'security': False, 'session_id': sid}
        try:
            ret = await _model_request_wrapper(data, model, timeout=LOAD_BALANCE_CONFIG.get('clean_chunks_timeout', 20))
            print(f'clean chunks ret keys:{ret.keys()}')
            judge_result = ret['choices'][0]['message']['content'].strip()
            if judge_result in ['是','否']:
                if '是' in judge_result:
                    chunk['content'] = chunk['content'].replace('、','.')
                    if '|' not in chunk['content']:
                        chunk['content'] = chunk['content'].replace('\n',' ')
                    return (1,chunk)
                else:
                    return (0,chunk)
            else:
                logger.error(f'[{sid}]clean chunks err ret:{judge_result},{prune_chunk}')
                return (-1,chunk)
        except Exception as e:
            logger.error(f'[{sid}]clean chunks failed, {e}')
            return (-2,chunk)

    tasks = [check_chunk_with_gemma(chunk) for chunk in chunks]
    results = await asyncio.gather(*tasks)
    print(f'======clean chunks results:{results}')
    results = [r[1] for r in results if r[0]!=0]
    logger.info(f'[{sid}]clean chunks result:{results}')
    return results

async def _rerank_chunks(question, chunks, sid):
    if RERANK_URL:
        prune_chunks=[chunk['content'].replace('\n',' ').replace('、','.') for chunk in chunks]
        data = {
            'model': RERANK_MDL,
            'query': question,
            'documents': prune_chunks
        }
        async with httpx.AsyncClient() as client:
            ret = await client.post(RERANK_URL, json=data)
            ret = ret.json()
            scores = [i['relevance_score'] for i in ret.get('results', [])]
    else:
        scores = [1.0] * len(chunks)
    results = [(score, chunk) for chunk, score in zip(chunks, scores)]
    return results

def switch_to_fallback_engine(retry_state):
    """切换到备选搜索引擎"""
    attempt_number = retry_state.attempt_number
    if attempt_number <= len(FALLBACK_ENGINES):
        new_engine = FALLBACK_ENGINES[attempt_number - 1]
        if hasattr(retry_state, 'args') and retry_state.args:
            data = retry_state.args[0]
            data['engine'] = new_engine
            logger.info(f"Switching to fallback engine: {new_engine}")


@retry(
    stop=stop_after_attempt(len(FALLBACK_ENGINES)),
    wait=wait_fixed(0.2),
    retry=retry_if_exception_type((Exception)),
    before_sleep=switch_to_fallback_engine,
    reraise=True
)
@log_model_call_async(key='call_search_api', extract_keys=['engine','sid','final_query'])
async def _call_search_api(data: dict, sid: str) -> Tuple[List[str], List[str]]:
    doc, href = [], []
    t0 = time.time()
    engine = data.get('engine', 'sogou')
    
    try:
        _timeout = 20 if data['search_expand'] else 10
        logger.info(f'[{sid}]web search request using engine {engine}: {data}')
        
        async with httpx.AsyncClient() as client:
            ret = await client.post(SEARCH_URL, json=data, timeout=_timeout)
            qret = ret.json()
            
            if type(qret) == dict:
                if 'data' in qret:
                    qret = qret['data']
                if 'code' in qret:
                    if qret['code'] != 10000:
                        raise Exception(f"Search API returned error: {qret}")
                    # 如果code为10000，继续处理数据，不直接返回
            
            qret = qret[:data['top_n']]
            logger.info(f'[{sid}]web search return: {qret}' +
                        f' time_cost:{time.time()-t0}')

            if type(qret) == list:
                for l in qret:
                    if 'body' in l and 'href' in l:
                        doc.append(l['body'].strip())
                        href.append(l['href'].strip())
                    else:
                        doc.append(str(l)[:2000])
                        href.append('http-unsolved')
            else:
                doc.append(str(ret.text)[:2000])
                href.append('http-unsolved')
                
    except Exception as e:
        logger.error(f'[{sid}]web search error using engine {engine}: {data},{e},{traceback.format_exc()}')
        raise
    
    return doc, href



async def request_search_oneshot(llm_kwargs):
    engine = llm_kwargs.get('engine', 'sogou')
    messages = llm_kwargs['messages']
    sid = llm_kwargs['session_id'] if 'session_id' in llm_kwargs else ''
    query = messages[0]['content'] if 'final_query' not in llm_kwargs else llm_kwargs['final_query']
    query = await _search_query_rewrite(query,model=llm_kwargs.get('model','hy'),sid=sid
                              ) if llm_kwargs.get('search_rewrite', False) else query

    data = {
        'text': query,
        'engine': engine,
        'domain_black_list': llm_kwargs.get('domain_black_list', ['youtube.com']),
        'domain_white_list': llm_kwargs.get('domain_white_list', None),
        'search_expand': llm_kwargs.get('expand', False) or llm_kwargs.get('search_expand', False),
        'top_n': llm_kwargs.get('top_n', 5),
        'agent_id': llm_kwargs.get('agent_id', "climate_tech_tanlive")
    }
    logger.info(f'[{sid}]web search request using engine {engine}: {data}')
    doc, href = await _call_search_api(data, sid=sid)

    # update prompt prefix
    try:
        prompt_prefix = llm_kwargs['prompt_prefix'].replace('\\n', '\n') if \
                'prompt_prefix' in llm_kwargs else dr_prompt
        ref_content = llm_kwargs['messages'][-1]['content']
        i_say = prompt_prefix.format(ref_content) + '\n'.join(doc)
                            
    except Exception as e:
        # logger.error(f'[{sid}] try to use prompt_prefix failed, use default prompt, {e}')
        return {'code':90060,'info':f'prompt_prefix failed: {e}, final search query: {query}'}
    
    logger.info(f'[{sid}]web search return: href:{href}, i_say:{i_say}')
    return {'i_say': i_say, 'reference': href, 'final_search_query':query}

@log_model_call_async(key='search_collection_chunks', extract_keys=['search_data','search_type','sid'])
async def search_collection_chunks(search_data, search_type="emb", sid=''):
    t0 = time.time()
    try:
        async with httpx.AsyncClient() as client:
            ret = await client.post(f'{GATE_URL_LOCAL}/collection/search', json=search_data, timeout=100)
            ret = json.loads(ret.text)
            ret4log = str(ret).replace('\n','')
            logger.info(f'[{sid}]collection query&return {search_type}: {search_data}; ret: {ret4log},'+
                        f' time_cost:{time.time()-t0}')
            if 'chunks' in ret['data']:
                return ret['data']['chunks']
            else:
                return ret['data']['data']
    except Exception as e:
        logger.info(f'[{sid}]collection query err {search_type}: {search_data},{traceback.format_exc()}')
        return []

@log_model_call_async(key='request_collection_oneshot', extract_keys=['all'])
async def request_collection_oneshot(llm_kwargs):
    # ---- copy from gpt3 start, rewrite later------
    text = llm_kwargs.get('text',None)
    sid = llm_kwargs['session_id'] if 'session_id' in llm_kwargs else ''
    if not text:
        raise HTTPException(status_code=400, detail='request must have valid `text` param')
    try:
        messages = llm_kwargs['text']
        if 'texts' in llm_kwargs:
            llm_kwargs.pop('texts')
        if isinstance(messages, list):
            llm_kwargs['messages'] = messages
        else:
            if messages[0]=='[' and messages[-1]==']':
                try:
                    llm_kwargs['messages'] = json.loads(messages)
                except:
                    logger.debug(f'`text` is pure str')
                    llm_kwargs['messages'] = [{'role': 'user', 'content': messages}]
            else:
                llm_kwargs['messages'] = [{'role': 'user', 'content': messages}]
        collection_name = llm_kwargs['collection_name']
    except AssertionError as e:
        logger.error(f'[{sid}]gpt/messages args err: {traceback.format_exc()}')
        return {'code':90001,'info': f'text/messages collection args err: {e}'}
    # ---- copy from gpt3 end------
    
    contents, doc_names = [], []
    messages = llm_kwargs['messages']
    top_n = llm_kwargs.get('top_n', 5)
    from_ = llm_kwargs.get('from_', 0)
    threshold = llm_kwargs.get('threshold', 0.6)
    text_weight = llm_kwargs.get('text_weight', 0)
    text_recall_top_n = llm_kwargs.get('text_recall_top_n', 0)
    lang = llm_kwargs.get('lang', 'zh')
    es_ins = llm_kwargs.get('es_ins', None)
    es_query = llm_kwargs.get('es_query', None)
    sid = llm_kwargs['session_id'] if 'session_id' in llm_kwargs else ''
    
    query = llm_kwargs['final_query'] if 'final_query' in llm_kwargs else  messages[-1]['content']

    docs,text_docs = [],[]
    data = {'collection_name': collection_name, 
                'text': query,
                'lang': lang,
                'from_': from_,
                'size': top_n,
                'threshold': threshold,
                'text_weight': text_weight,
                'session_id': sid}
    if es_ins:
        data['es_ins'] = es_ins
    if es_query:
        data['es_query'] = es_query
    import asyncio
    
    # 准备向量搜索参数
    vector_search_data = data.copy()
    
    # 准备文本搜索参数
    text_search_data = None
    if text_recall_top_n:
        text_search_data = data.copy()
        text_search_data['text_weight'] = 1
        text_search_data['size'] = text_recall_top_n
        if text_search_data.get('debug'):
            print('===text_search_data', text_search_data)
    
    # 并发执行搜索
    search_tasks = [search_collection_chunks(search_data=vector_search_data, search_type="emb", sid=sid)]
    if text_search_data:
        search_tasks.append(search_collection_chunks(search_data=text_search_data, search_type="text", sid=sid))
    
    results = await asyncio.gather(*search_tasks)
    
    # 解析结果
    docs = results[0]
    text_docs = results[1] if text_recall_top_n else []
            

    if not docs and not text_docs:
        logger.info(f'[{sid}]collection find no similar document to: {query}')
        return {'i_say': '', 'doc_names': '', 'raw_recall':[]}
    else:
        docs = [doc for doc in docs if doc['score'] > threshold]
        cleaned_chunk_ids = [i['id'] for i in docs]
        if llm_kwargs.get('clean_chunks', True):
            cleaned_chunks = await clean_chunks(query, docs+text_docs, sid=sid)
            raw_contents = [i['raw_content'] for i in cleaned_chunks]
            contents = [i['content'] for i in cleaned_chunks]
            doc_names = [i['doc_name'] for i in cleaned_chunks]
            cleaned_chunk_ids = [i['id'] for i in cleaned_chunks]
        else:
            contents = [doc['content'] for doc in docs+text_docs]
            raw_contents = contents
            doc_names = [doc['doc_name'] for doc in docs+text_docs]
        for i,content in enumerate(contents):
            contents[i] = f'\n{i+1}、'+content.replace(r'\r\n', '\n').replace('\n',' ')\
                    if '|' not in content else f'\n{i+1}、'+content

    if not contents:
        return {'i_say': '', 'doc_names': '', 'raw_docs':docs, 'raw_text_docs':text_docs}

    try:
        prompt_prefix = llm_kwargs['prompt_prefix'].replace('\\n', '\n') if \
                'prompt_prefix' in llm_kwargs else dr_prompt
        ref_content = messages[-1]['content']
        i_say = prompt_prefix.format(ref_content) + '\n'.join(contents)
    except Exception as e:
        # logger.error(f'[{sid}]try to use prompt_prefix failed, use default prompt')
        raise ValueError(f'prompt_prefix format err, {e}')
    # logger.info(f'prompt: {i_say}')
    # 返回值为：prompt，参考资料
    logger.info(f'[{sid}]collection search return: cleaned_chunk_ids:{cleaned_chunk_ids}, doc_names:{doc_names}, i_say:{i_say}')
    return {'i_say': i_say, 'chunk_ids':cleaned_chunk_ids,
            'contents':raw_contents, 'doc_names': doc_names,
             'raw_docs':docs, 'raw_text_docs':text_docs}


@gpt_app.post("/search_collection_oneshot")
async def search_collection_oneshot(llm_kwargs = Depends(parse_fastapi_data)):
    ret = await request_collection_oneshot(llm_kwargs)
    return {'code':10000, 'data':ret}



@gpt_app.post("/chat_stream")
async def chat_stream(llm_kwargs = Depends(parse_fastapi_data)):
    sid = llm_kwargs.get('session_id', '')
    text = llm_kwargs.get('text')
    if not text and not llm_kwargs.get('messages'):
        raise HTTPException(status_code=400, 
                            detail='chat request must have valid `text` or `messages` param')
    
    # text字段为json，则拆分成str的text和role，否则设置role默认为user
    try:
        if 'texts' in llm_kwargs:
            llm_kwargs.pop('texts')
            
        if 'messages' in llm_kwargs and isinstance(llm_kwargs['messages'], list):
            pass
        elif isinstance(text, list):
            llm_kwargs['messages'] = text
        else:
            if text[0]=='[' and text[-1]==']':
                try:
                    json_messages = json.loads(text)
                    llm_kwargs['messages'] = [{'role': 'user', 'content': text}] \
                        if type(json_messages[0])==int else json_messages
                except:
                    logger.debug(f'`text` is pure str')
                    llm_kwargs['messages'] = [{'role': 'user', 'content': text}]
            else:
                llm_kwargs['messages'] = [{'role': 'user', 'content': text}]
        messages = llm_kwargs['messages']
    except AssertionError as e:
        logger.error(f'[{sid}]gpt/messages args error: {traceback.format_exc()}')
        return {'code':90001,'info': f'text/messages args error: {e}'}

    # process query text for search / doc enhancement
    reference,raw_reference,query = '',[],messages[-1]['content']
    if llm_kwargs.get('collection_name') or llm_kwargs.get('search'):
        query = (messages[-1]['content'] if len(messages) < 3 
                else await _use_multiround_summary(messages, sid=sid))
        llm_kwargs['final_query'] = query

    # check prompt type
    prompt_type, is_time_query = '', False
    if llm_kwargs.get('judge_prompt_type'):
        prompt_types = await asyncio.to_thread(_judge_prompt_type_local, query)
        prompt_type = prompt_types['type']
        is_time_query = prompt_types['time']
        if is_time_query:
            query = await _user_time_rewrite(query)
            llm_kwargs['final_query'] = query

    if llm_kwargs.get('collection_name') and prompt_type != 'chat':
        logger.debug('try doc enhancement')
        ret = await request_collection_oneshot(llm_kwargs)
        if 'code' in ret:
            return ret
        col_prompt = ret.get('i_say')
        raw_reference = ret.get('doc_names',[])
        reference = '\n'.join(raw_reference)
        # 没有找到参考文献
        if not reference:
            if not llm_kwargs.get('search'):
                logger.debug(f'[{sid}]document retrival result empty')
                return {'code':90010,'gpt':'问题与文档内容不相关',
                        'info':{'final_query':query if query else messages[-1]['content']}
                        }
        else:
            llm_kwargs['messages'][-1]['content'] = col_prompt
            llm_kwargs['enhancement'] = 'doc'
            
    if not reference and llm_kwargs.get('search') and prompt_type != 'chat':
        search_ret = await request_search_oneshot(llm_kwargs)
        if 'code' in search_ret:
            return search_ret
        llm_kwargs['messages'][-1]['content'] = search_ret.get('i_say') 
        raw_reference = search_ret.get('reference',[])
        reference = '\n'.join(raw_reference)
        llm_kwargs['enhancement'] = 'search'
        llm_kwargs['final_search_query'] = search_ret.get('final_search_query')

    llm_kwargs['model'] = llm_kwargs.get('model','hy')
    if (not prompt_type or prompt_type=='chat') and llm_kwargs.get('model4chat'):
        llm_kwargs['model'] = llm_kwargs['model4chat']
    if (not prompt_type or prompt_type=='complex') and llm_kwargs.get('model4complex'):
        llm_kwargs['model'] = llm_kwargs['model4complex']


    # Prepare metadata to send after stream
    metadata = {}
    if reference:
        metadata['ref'] = reference
        metadata['references'] = raw_reference
    if llm_kwargs.get('enhancement'):
        metadata['enhancement'] = llm_kwargs['enhancement']
    
    metadata['data'] = {}
    if llm_kwargs.get('final_query', ''):
        metadata['data']['final_query'] = llm_kwargs['final_query']
    metadata['data']['prompt_type'] = prompt_type
    metadata['data']['prompt_time'] = is_time_query
    if llm_kwargs.get('final_search_query', ''):
        metadata['data']['final_search_query'] = llm_kwargs['final_search_query']

    logger.info(f'[{sid}] /chat_stream request data json: {str(llm_kwargs)[:1000]}...')

    # http request
    if llm_kwargs.get('stream') == False:
        try:
            gptsay = await _model_request_wrapper(llm_kwargs,llm_kwargs['model'])
            if 'choices' in gptsay:
                # 不修改原数据的方式
                response = gptsay['choices'][0]['message']
                metadata['data']['gpt'] = response['content']
                metadata['data']['session_id'] = sid
            if '_content' in gptsay:
                metadata['data']['response'] = json.loads(gptsay.pop('_content'))
                metadata['data']['gpt'] = gptsay['data']['response'].pop('content')
                metadata['data']['session_id'] = sid
            metadata['code'] = 10000
            metadata['info'] = ''
            if 'response' in metadata['data'] and 'content' in metadata['data']['response']:
                metadata['data']['response']['content'] = ''
            metadata['data']['session_id'] = sid
            return metadata
        except Exception as e:
            logger.error(f"[{sid}] chat_stream non-stream error: {e}")
            error_response = {'code': 90020,'info': f'Non-stream model request failed: {str(e)}'}
            return error_response
    
    # stream request
    try:
        stream_response = await request_stream_with_load_balance(llm_kwargs)
        async def stream_with_metadata():
            # First send metadata as SSE event
            metadata['data']['session_id'] = sid
            yield ('data: ' + json.dumps(metadata) + '\n\n').encode('utf-8')
            
            # Then stream the response chunks
            if isinstance(stream_response, StreamingResponse):
                async for chunk in stream_response.body_iterator:
                    # Let other coroutines run
                    await asyncio.sleep(0)
                    if chunk:
                        yield chunk
            else:
                # Handle non-streaming responses
                yield ('data: ' + json.dumps(stream_response) + '\n\n').encode('utf-8')

        return StreamingResponse(
            stream_with_metadata(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )
    except Exception as e:
        logger.error(f"[{sid}] chat_stream error: {e}")
        error_response = {'code': 90020,'info': f'Stream model request failed: {str(e)}'}
        return error_response


@gpt_app.post("/gpt3")
@gpt_app.post("/gpt3/dev")
@gpt_app.post("/gpt3/test")
async def gpt(llm_kwargs = Depends(parse_fastapi_data)):
    sid = llm_kwargs.get('session_id', '')
    text = llm_kwargs.get('text')
    t_model_check = llm_kwargs.get('model')
    if t_model_check not in REMOTE_MODELS and t_model_check not in OPENAI_MODELS    :
        raise HTTPException(status_code=400, detail='model not available')
    t_model_check = llm_kwargs.get('model4chat')
    if t_model_check and t_model_check not in REMOTE_MODELS and t_model_check not in OPENAI_MODELS:
        raise HTTPException(status_code=400, detail='model4chat not available')
    t_model_check = llm_kwargs.get('model4complex')
    if t_model_check and t_model_check not in REMOTE_MODELS and t_model_check not in OPENAI_MODELS:
        raise HTTPException(status_code=400, detail='model4complex not available')

    messages = llm_kwargs.get('messages')
    if not text and not messages:
        raise HTTPException(status_code=400, detail='request must have valid `text` or `messages`')
    
    # text字段为json，则拆分成str的text和role，否则设置role默认为user
    try:
        if 'texts' in llm_kwargs:
            llm_kwargs.pop('texts')
        
        if 'messages' in llm_kwargs and isinstance(llm_kwargs['messages'], list):
            pass
        elif isinstance(text, list):
            llm_kwargs['messages'] = text
        else:
            if text[0]=='[' and text[-1]==']':
                try:
                    json_messages = json.loads(text)
                    llm_kwargs['messages'] = [{'role': 'user', 'content': text}] \
                        if type(json_messages[0])==int else json_messages
                except:
                    logger.debug(f'`text` is pure str')
                    llm_kwargs['messages'] = [{'role': 'user', 'content': text}]
            else:
                try:
                    json_messages = json.loads(text)
                    llm_kwargs['messages'] = [{'role': 'user', 'content': text}] \
                        if type(json_messages[0])==int else json_messages
                except:
                    logger.debug(f'`text` is pure str')
                    llm_kwargs['messages'] = [{'role': 'user', 'content': text}]
        messages = llm_kwargs['messages']
    except AssertionError as e:
        logger.error(f'[{sid}]gpt/messages args error: {traceback.format_exc()}')
        return {'code':90001,'info': f'text/messages args error: {e}'}

    # process query text for search / doc enhancement
    reference,raw_reference,query = '',[],messages[-1]['content']
    if llm_kwargs.get('collection_name') or llm_kwargs.get('search'):
        query = (messages[-1]['content'] if len(messages) < 3 
                else await _use_multiround_summary(messages, sid=sid))
        llm_kwargs['final_query'] = query

    # check prompt type
    prompt_type, is_time_query = '', False
    if llm_kwargs.get('judge_prompt_type'):
        prompt_types = await asyncio.to_thread(_judge_prompt_type_local, query)
        prompt_type = prompt_types['type']
        is_time_query = prompt_types['time']
        if is_time_query:
            query = await _user_time_rewrite(query)
            llm_kwargs['final_query'] = query
        
    if llm_kwargs.get('collection_name') and prompt_type != 'chat':
        logger.debug('try doc enhancement')
        ret = await request_collection_oneshot(llm_kwargs)
        if 'code' in ret:
            return ret
        col_prompt = ret.get('i_say')
        raw_reference = ret.get('doc_names',[])
        reference = '\n'.join(raw_reference)
        # 没有找到参考文献
        if not reference:
            if not llm_kwargs.get('search'):
                logger.debug(f'[{sid}]document retrival result empty')
                return {'code':90010,'gpt':'问题与文档内容不相关','info':
                        {'final_query':query if query else messages[-1]['content']}
                        }
        else:
            llm_kwargs['messages'][-1]['content'] = col_prompt
            llm_kwargs['enhancement'] = 'doc'
            
    if not reference and llm_kwargs.get('search') and prompt_type != 'chat':
        search_ret = await request_search_oneshot(llm_kwargs)
        llm_kwargs['messages'][-1]['content'] = search_ret.get('i_say') 
        raw_reference = search_ret.get('reference',[])
        reference = '\n'.join(raw_reference)
        llm_kwargs['enhancement'] = 'search'
        llm_kwargs['final_search_query'] = search_ret.get('final_search_query')

    llm_kwargs['model'] = llm_kwargs.get('model','hunyuan-standard')
    if prompt_type=='chat' and llm_kwargs.get('model4chat'):
        llm_kwargs['model'] = llm_kwargs['model4chat']
    if prompt_type=='complex' and llm_kwargs.get('model4complex'):
        llm_kwargs['model'] = llm_kwargs['model4complex']

    logger.info(f'[{sid}] /gpt request data json: {str(llm_kwargs)[:1000]}...')

    # make request
    if llm_kwargs.get('stream', False):
        logger.info(f'[{sid}]gpt: stream True, force set to False')
        llm_kwargs['stream']=False

    model = llm_kwargs.get('model', '')
    try:
        gptsay = await _model_request_wrapper(llm_kwargs, model, timeout=60)
    except Exception as e:
        raise ValueError(f'llm return parse error: {traceback.format_exc()}')
    
    if reference:
        gptsay['ref'] = reference
        gptsay['references'] = raw_reference
    if llm_kwargs.get('enhancement'):
        gptsay['enhancement'] = llm_kwargs['enhancement']

    # metadata
    gptsay['data'] = gptsay.get('data',{})
    if llm_kwargs.get('final_query', ''):
        gptsay['data']['final_query'] = llm_kwargs['final_query']
    gptsay['data']['prompt_type'] = prompt_type
    gptsay['data']['prompt_time'] = is_time_query
    if llm_kwargs.get('final_search_query', ''):
        gptsay['data']['final_search_query'] = llm_kwargs['final_search_query']

    # 统一返回格式
    if 'choices' in gptsay:
        gptsay['data']['response'] = gptsay.pop('choices')[0]['message']
        gptsay['data']['gpt'] = gptsay['data']['response'].pop('content')
    if '_content' in gptsay:
        gptsay['data']['response'] = json.loads(gptsay.pop('_content'))
        gptsay['data']['gpt'] = gptsay['data']['response'].pop('content')
    
    return gptsay


@gpt_app.post('/chat')
async def chat(req_kwargs = Depends(parse_fastapi_data)):
    sid = req_kwargs.get('session_id')
    ret = await gpt(req_kwargs)
    if 'code' in ret and ret['code']>20000: # return error msg directly
        return ret
    try:
        ans = ret['data']['gpt'] if 'gpt' in ret['data'] else ''
        # 文档增强相似性结果不好，搜索开关打开则进入搜索增强
        if '无法回答' in ans or '并未提供' in ans or '无法找到' in ans:
            if req_kwargs.get('search'):
                logger.info(f'[{sid}]doc retrieve empty, search directly')
                search_alone_req_kwargs={k: v for k, v in req_kwargs.items() if k != 'collection_name'}
                ret = await gpt(search_alone_req_kwargs)
                ans = ret['data']['gpt']
    except Exception as e:
        logger.error(f'[{sid}]chat post process err: {e}')
        return {'code': 90002, 'info': f'chat post process err: {e}'}

    ret['data']['gpt'] = ans
    ret['code'] = 10000
    ret['info'] = ''
    if 'response' in ret['data'] and 'content' in ret['data']['response']:
        ret['data']['response']['content'] = ''
    ret['data']['session_id'] = sid
    return ret


# if __name__ == '__main__':
#     import uvicorn
#     uvicorn.run('gpt3:app', host="0.0.0.0", port=8001, reload=True)
