import time

extract_profile_from_conversation_prompt = f'''[角色] 用户画像分析器
[任务] 从对话提取用户特征，输出JSON：
{text}
---
输出规则：
1. 若涉及新特征（如“我讨厌洋葱”），添加键值对：{{"hate_onion": true}}
2. 若更新旧特征（如“现在改用Java了”），修改键值：{{"preferred_lang": "Java"}}
3. 冲突时标记待验证：{{"_conflict": "曾记录喜欢Java，本次称改用Python"}}
输出示例：{{"traits": {{"preferred_lang": "Python"}}, "knowledge_base": {{"last_project": "AI记忆系统"}}}}'''


summarize_conversation_prompt = f'''[角色] 对话主题聚类器
[任务] 当前时间是{time.strftime("%Y年%m月%d日", time.localtime())}，根据1天的对话，生成多个主题标签和摘要：
示例：
今天的对话如下：
{text}
---
输出规则：
1. 提取对话中的主要观点和讨论点
2. 保持对话的连贯性和逻辑性
3. 使用简洁明了的语言
输出示例：[{{"2010年1月7日#Gumbel分布讨论": "用户询问什么是Gumbel分布，并讨论了Gumbel分布的性质和应用",
"2010年1月7日#LLM应用架构讨论": "用户咨询RAG系统设计，并讨论Python与Java的选型差异，最后确定使用MemoryOS作为记忆层"}}]'''


image_in_text_rag_prompt= f'''# Role: 智能回答助手，名字是小海豹
任务：从后面的资料中提取相关内容用第一人称回答问题：{text}

## 规则  
    - 每段回答的最后用[n]表明这段回答参考的是第几个材料，n对应材料的顺序，一定是int，不要出现`[n]`
    - 如果参考资料中有图片文件可以辅助回答问题，则根据图片描述利用占位符进行图文混排，使图片出现在合适的位置
    - 图片内容严格使用统一标识符 `[IMG: <ID>]`作为占位符，其中`<ID>` 替换为图片文件名

**输出示例**：  
微波消融治疗肝癌的步骤如下：  
1. 在超声引导下将电极针插入肿瘤中心
[IMG: fig_2024050112.png]  
2. 启动射频发生器产生热效应...  

以下相关资料：
'''