# encoding: utf-8
import re
from typing import List, Optional


def merge_lines(text):
    if not text.strip().startswith('#'):
        text = '\n# '+text
    if '\\n' in text:
        text=text.replace('\\n','\n')
    #多换行变双换行，多空格变双空格
    text=re.sub(r'(\s)\1+', r'\1\1', text)
    # 清理中文字符间的空格
    chinese_with_space_pattern=r'([\u4e00-\u9fff])\s+([\u4e00-\u9fff])'
    while re.search(chinese_with_space_pattern, text):
        text=re.sub(chinese_with_space_pattern, r'\1\2', text)

    lines = text.split('\n')
    merged_lines = []
    current_line = ''
    current_prefix = None

    # 遍历每一行文本进行处理
    for line in lines:
        # 跳过空行
        if line.strip().strip('`')=='':
            continue
        # 跳过表格、图片等标题行
        caption = re.match(r'^#+\s(?:Table|Box|Figure)\s\d.*', line)
        if caption:
            continue
        # 跳过单个字母的标题行
        singleletter=re.match(r'^#+\s.$', line)
        if singleletter:
            continue
        # 检查是否以#开头的标题行
        prefix = re.match(r'^(#+)', line)
        if prefix:
            # 获取#号的数量作为标题级别
            prefix = prefix.group(1)
            # 如果与当前标题级别相同,合并内容
            if prefix == current_prefix:
                current_line += line[len(prefix):]
            # 如果标题级别不同,作为新段落
            else:
                if current_line:
                    merged_lines.append(current_line)
                current_line = line
                current_prefix = prefix
        # 非标题行的处理        
        else:
            # 如果当前行不是以标点符号结尾,则与下一行合并
            if current_line and not re.search(r'[。！？.!?]$', current_line.strip()):
                current_line += ' ' + line
            # 否则作为新段落
            else:
                if current_line:
                    merged_lines.append(current_line)
                current_line = line
                current_prefix = None
    # 处理最后一行
    if current_line:
        merged_lines.append(current_line)
    # 合并所有行并返回
    return '\n'.join(merged_lines)




def enhanced_recursive_split(
    text: str,
    separators: Optional[List[str]] = None,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    chunk_min: Optional[int] = None,
) -> List[str]:
    """
    增强版递归字符分割器
    :param text: 待分割文本
    :param chunk_size: 块最大字符数（建议500-2000）
    :param chunk_overlap: 块间重叠字符数（建议10%-20% chunk_size）
    :param separators: 优先级递减的分割符列表
    :param min_length: 最小块长度（强制合并小块的阈值）
    :return: 分割后的文本块列表
    """
    # 默认中文友好分隔符
    default_separators = ["\n\n", "\n", "。", "？", "！", "；", "，", ". "]
    separators = separators or default_separators

    def split_with_separator(text: str, sep: str) -> List[str]:
        """使用分隔符分割文本，并保留分隔符"""
        if not sep:
            return [text]
        
        # 使用正则表达式来分割，保留所有分隔符
        pattern = f"({re.escape(sep)}+)"
        parts = re.split(pattern, text)
        
        # 重新组合部分，确保分隔符被正确保留
        result = []
        for i in range(0, len(parts)-1, 2):
            if i+1 < len(parts):
                result.append(parts[i] + parts[i+1])
            else:
                result.append(parts[i])
        
        # 处理最后一个部分
        if len(parts) % 2 == 1:
            result.append(parts[-1])
            
        return result

    def process_chunk(chunk: str, current_seps: List[str], depth: int = 0) -> List[str]:
        """处理单个文本块"""
        if len(chunk) <= chunk_size:
            return [chunk]
        
        if not current_seps:
            # 如果没有更多分隔符，按固定长度切分
            result = []
            start = 0
            while start < len(chunk):
                end = min(start + chunk_size, len(chunk))
                result.append(chunk[start:end])
                # 确保重叠部分不会导致重复
                if end < len(chunk):
                    start = end - chunk_overlap
                    # 如果重叠部分导致重复，调整起始位置
                    while start > 0 and chunk[start-1] == chunk[end-1]:
                        start -= 1
                        end -= 1
                else:
                    start = end
            return result

        # 使用当前分隔符分割
        current_sep = current_seps[0]
        parts = split_with_separator(chunk, current_sep)
        
        # 处理每个部分
        result = []
        current = ""
        for part in parts:
            if len(current) + len(part) <= chunk_size:
                current += part
            else:
                if current:
                    result.append(current)
                # 对过长的部分使用下一级分隔符递归处理
                result.extend(process_chunk(part, current_seps[1:], depth + 1))
                current = ""
        
        if current:
            result.append(current)
        
        return result

    # 执行分割
    chunks = process_chunk(text, separators)

    # 处理最小长度要求
    if chunk_min is not None:
        merged = []
        current = ""
        
        for chunk in chunks:
            # 检查当前块是否以分隔符结尾
            ends_with_sep = any(chunk.endswith(sep) for sep in separators)
            # 检查下一个块是否以分隔符开头
            next_starts_with_sep = any(chunk.startswith(sep) for sep in separators)
            
            # 如果当前块小于chunk_min，尝试合并
            if len(current) < chunk_min:
                # 如果当前块以分隔符结尾，或者下一个块以分隔符开头，直接合并
                if ends_with_sep or next_starts_with_sep:
                    current += chunk
                else:
                    # 检查是否有重复内容
                    if current and chunk:
                        # 找到最长的公共后缀和前缀
                        overlap = 0
                        for i in range(1, min(len(current), len(chunk)) + 1):
                            if current[-i:] == chunk[:i]:
                                overlap = i
                        if overlap > 0:
                            current += chunk[overlap:]
                        else:
                            current += chunk
                    else:
                        current += chunk
            # 如果当前块已经达到chunk_min，且合并后不超过chunk_size，则合并
            elif len(current) + len(chunk) <= chunk_size:
                current += chunk
            else:
                if current:
                    merged.append(current)
                current = chunk
        
        # 处理最后一个块
        if current:
            # 如果最后一个块小于chunk_min，尝试与前面的块合并
            if len(current) < chunk_min and len(merged) > 0:
                # 检查最后一个合并块是否以分隔符结尾
                last_ends_with_sep = any(merged[-1].endswith(sep) for sep in separators)
                # 检查当前块是否以分隔符开头
                current_starts_with_sep = any(current.startswith(sep) for sep in separators)
                
                if last_ends_with_sep or current_starts_with_sep:
                    merged[-1] += current
                else:
                    # 检查是否有重复内容
                    overlap = 0
                    for i in range(1, min(len(merged[-1]), len(current)) + 1):
                        if merged[-1][-i:] == current[:i]:
                            overlap = i
                    if overlap > 0:
                        merged[-1] += current[overlap:]
                    else:
                        merged[-1] += current
            else:
                merged.append(current)
        
        # 最后检查是否还有小于chunk_min的块
        final_merged = []
        current = ""
        for chunk in merged:
            if len(chunk) < chunk_min:
                if current:
                    current += chunk
                else:
                    current = chunk
            else:
                if current:
                    final_merged.append(current)
                final_merged.append(chunk)
                current = ""
        
        if current:
            final_merged.append(current)
        
        chunks = final_merged

    return chunks




def merge_small_chunks(chunks: List[str], chunk_size: int, chunk_min: int) -> List[str]:
    """合并小于最小长度的块 for enhanced_recursive_split_v2"""
    if not chunk_min:
        return chunks
        
    merged = []
    current = ""
    for chunk in chunks:
        if len(current) + len(chunk) <= chunk_size:
            current += chunk
        else:
            if current:
                merged.append(current)
            current = chunk
    if current:
        merged.append(current)
    return merged


def enhanced_recursive_split_v2(
    text: str,
    separators: Optional[List[str]] = None,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    chunk_min: Optional[int] = None,
) -> List[str]:
    """
    增强版递归字符分割器
    :param text: 待分割文本
    :param chunk_size: 块最大字符数（建议500-2000）
    :param chunk_overlap: 块间重叠字符数（建议10%-20% chunk_size）
    :param separators: 优先级递减的分割符列表
    :param min_length: 最小块长度（强制合并小块的阈值）
    :return: 分割后的文本块列表
    """
    # 默认中文友好分隔符
    default_separators = ["\n\n", "\n", "。", "？", "！", "；", "，", "."]
    separators = separators or default_separators

    # 按优先级依次尝试分割
    for sep in separators:
        if sep in text:
            # 分割并保留分隔符
            segments = []
            parts = text.split(sep)
            for i, part in enumerate(parts):
                if i > 0:  # 非第一个部分，添加分隔符
                    segments.append(sep + part)
                else:
                    segments.append(part)
            
            # 处理每个分割后的片段
            result = []
            current_chunk = ""
            
            for segment in segments:
                if len(current_chunk) + len(segment) <= chunk_size:
                    current_chunk += segment
                else:
                    if current_chunk:
                        result.append(current_chunk)
                    current_chunk = segment
            
            if current_chunk:
                result.append(current_chunk)
            
            # 如果分割后的片段都小于chunk_size，直接返回
            if all(len(chunk) <= chunk_size for chunk in result):
                return merge_small_chunks(result, chunk_size, chunk_min)
            
            # 否则继续用下一个分隔符尝试分割
            continue
    
    # 如果没有找到合适的分隔符，或者分割后仍有大块，则按字符数分割
    result = []
    for i in range(0, len(text), chunk_size - chunk_overlap):
        chunk = text[i:i + chunk_size]
        if chunk:
            result.append(chunk)
    
    return merge_small_chunks(result, chunk_size, chunk_min)



if __name__ == '__main__':
    with open('/data/dat/rag/split_cpu_overhead_case.txt','r') as f:
        text = f.read()
    import time
    t0=time.time()
    r=enhanced_recursive_split(text,chunk_size=319,chunk_overlap=9,chunk_min=100)
    print('===splitted',len(text),len(r),len(''.join(r)),time.time()-t0)
    for s in r:
        if s not in text:
            print('===UNMATCHED===',s)
        if len(s) < 100:
            print('===SMALL===',s)
    print('=====\n',r[:3])
