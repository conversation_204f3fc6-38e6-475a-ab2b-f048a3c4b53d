#encoding=utf-8
import logging
from ssv_logger import ssv_logger
ssv_logger('agent.log')
logger = logging.getLogger(__name__)
import json
import traceback
import uuid
from typing import List, Union, Optional
from fastapi import Request, File, UploadFile, Header, HTTPException
from pypinyin import lazy_pinyin
from config import IMAGE_URL_BASE64_MODELS


def generate_session_id():
    return str(uuid.uuid4())


def safe_str(obj, max_length=2000):
    try:
        s = str(obj)
        return s[:max_length] + '...' if len(s) > max_length else s
    except:
        return str(type(obj))
        
async def parse_fastapi_data(request: Request, files: List[UploadFile] = File(None),
                             x_api_key: Optional[str] = Header(None, alias="X-API-Key"),
                             authorization: Optional[str] = Header(None)):
    try:
        headers = dict(request.headers)
        if request.method == 'POST' and request.headers.get("Content-Type") == "application/json":
            data = await request.json()
            data = dict(data)
        else:
            data = await request.form()
            parsed_data = {}
            for key in data.keys():
                values = data.getlist(key)
                if len(values) == 1:
                    parsed_data[key] = values[0]  # 单值时直接取元素
                else:
                    parsed_data[key] = values     # 多值时保留列表
            data = parsed_data
        
        if 'session_id' not in data:
            data['session_id']=generate_session_id()

        # 处理API认证 - 优先使用X-API-Key，其次使用Authorization
        api_key = None
        if x_api_key:
            api_key = x_api_key
        elif authorization and authorization.startswith('Bearer '):
            api_key = authorization.replace('Bearer ', '')

        if api_key:
            data['agent_id'] = api_key
        # readfile
        text = data.get('text', '')
        try:
            text = json.loads(text)
            data['text'] = text
        except:
            pass
        if type(text) == int:
            data['text'] = str(text)
            text = str(text)
        file_name = data.get('file_name', f'tmp_{generate_session_id()[-5:]}.txt')
        if len(text) and isinstance(text, str):
            data['texts'] = [[file_name, text]]
        else:
            data['texts'] = []
            if files:
                # use parse_files to store non-text files for later use
                from utils.text_loader import parse_file
                parsed_text = await parse_file(files[0],ftype='binary')
                data['texts'].append(parsed_text)
            if data.get('b64file'):
                parsed_text = await parse_file(data['b64file'],ftype='base64')
                data['texts'].append(parsed_text)
            if data.get('file_url'):
                parsed_text = await parse_file(data['file_url'],ftype='url')
                data['texts'].append(parsed_text)
        
        # 处理messages中的image_url，根据模型配置决定是否转换为base64
        model = data.get('model', '')
        sid = data.get('session_id', '')
        if model in IMAGE_URL_BASE64_MODELS:
            # 处理text字段中的image_url
            if 'text' in data and isinstance(data['text'], list):
                messages = data['text']
                await _process_image_urls(messages, sid)
            
            # 处理messages字段中的image_url
            if 'messages' in data and isinstance(data['messages'], list):
                messages = data['messages']
                await _process_image_urls(messages, sid)

        # parse completed
        header4log = safe_str(headers)
        data4log = safe_str(data)
        logger.info(f'[{sid}]REQuest headers&data: {header4log}, data: {data4log}...')

        # document related
        collection_name = data.get('collection_name')
        if collection_name:
            collection_name = ''.join(lazy_pinyin(collection_name))
            data['collection_name'] = collection_name
            if not data.get('user_name') and len(collection_name.split('-'))>1:
                data['user_name'] = collection_name.split('-',1)[0]
            if not data.get('model_name') and len(collection_name.split('-'))>1:
                data['model_name'] = collection_name.split('-',1)[1]

        # restore bool if converted to str
        for k, v in data.items():
            if isinstance(v, str) and v.lower() in ['true', 'false']:
                data[k] = True if v.lower() == 'true' else False

        # remove empty values
        try:
            data = {k: v for k, v in data.items() if v or type(v)==bool}
        except:
            logger.error(f'parse fastapi err, value not if-able:{data}')
        return data
    
    except Exception as e:
        logger.error(f'query parse error: {e}, {traceback.format_exc()}')
        raise HTTPException(
            status_code=400, detail=f'query parse error: {e}')


async def _process_image_urls(messages, sid):
    """处理消息中的所有image_url，将其转换为base64格式"""
    import httpx
    import base64
    
    for i, message in enumerate(messages):
        if isinstance(message.get('content'), list):
            for j, content in enumerate(message['content']):
                if content.get('type') == 'image_url' and content.get('image_url', {}).get('url'):
                    url = content['image_url']['url']
                    # 如果URL已经是base64格式，则跳过
                    if url.startswith('data:'):
                        continue
                        
                    try:
                        logger.info(f'[{sid}] Converting URL to base64: {url}')
                        async with httpx.AsyncClient() as client:
                            response = await client.get(url, timeout=60)
                            if response.status_code == 200:
                                # 根据Content-Type确定MIME类型
                                content_type = response.headers.get('Content-Type', 'application/octet-stream')
                                # 转换为base64
                                file_base64 = base64.b64encode(response.content).decode('utf-8')
                                # 替换URL为base64
                                content['image_url']['url'] = f"data:{content_type};base64,{file_base64}"
                                logger.info(f'[{sid}] Successfully converted to base64 with type {content_type}')
                            else:
                                logger.error(f'[{sid}] Failed to download file: {response.status_code}')
                    except Exception as e:
                        logger.error(f'[{sid}] Error converting to base64: {e}')

