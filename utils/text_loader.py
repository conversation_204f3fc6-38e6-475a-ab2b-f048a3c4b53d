import io
import re
import os
import time
import sys
import tempfile
import fitz
import json
import pdfplumber
import base64
import httpx
# import textract
from docx import Document
from pptx import Presentation
import hashlib
import traceback
from fastapi import FastAPI, HTTPException, Request, File, UploadFile
from typing import Optional, List, Dict, Mapping, Any

import logging
from ssv_logger import ssv_logger
ssv_logger('text_loader.log')
logger = logging.getLogger(__name__)


async def simple_parse_fastapi(request: Request, files: List[UploadFile] = File(None)):
    try:
        headers = dict(request.headers)
        if request.method == 'POST' and request.headers.get("Content-Type") == "application/json":
            data = await request.json()
            data = dict(data)
        else:
            data = await request.form()
            parsed_data = {}
            for key in data.keys():
                values = data.getlist(key)
                if len(values) == 1:
                    parsed_data[key] = values[0]  # 单值时直接取元素
                else:
                    parsed_data[key] = values     # 多值时保留列表
            data = parsed_data

        # readfile
        data['texts']=[]
        if files:
            if isinstance(files, UploadFile):
                files = [files]
            # use parse_files to store non-text files for later use
            for file in files:
                data['texts'].append(await parse_file(file))
        # parse completed
        header4log=str(headers).replace('\n','')
        data4log=str(data).replace('\n','')
        logger.info(f'REQuest headers&data: {header4log}, data: {data4log[:2000]}...')
    
    except Exception as e:
        logger.error(f'query parse error: {e}, {traceback.format_exc()}')
        raise HTTPException(
            status_code=400, detail=f'query parse error: {e}')




def clear_temp_files(temp_path):
    current_time = time.time()
    for root_path, _, files in os.walk(temp_path):
        for file in files:
            f_path = os.path.join(root_path, file)
            file_creation_time = os.path.getctime(f_path)
            file_age = current_time - file_creation_time
            # 删除5分钟前的文件
            if file_age > 5 * 60:
                os.remove(f_path)
                # logger.info(f'clearing temp files {f_path} successfully')



def parse_pdf_file(file_path):
    ret_text = ''
    with pdfplumber.open(file_path) as pdf:
        for page in pdf.pages:
            # 提取所有表格的坐标和内容
            tables = page.extract_tables()
            table_bboxes = [table.bbox for table in page.find_tables()]
            
            # 提取所有非表格区域的文本
            non_table_text = ""
            for word in page.extract_words():
                # 检查当前单词是否在表格区域内
                in_table = any(
                    (word["x0"] >= bbox[0] and word["x1"] <= bbox[2] and
                     word["top"] >= bbox[1] and word["bottom"] <= bbox[3])
                    for bbox in table_bboxes
                )
                if not in_table:
                    non_table_text += word["text"] + " "
            
            # print("纯文本内容:", non_table_text.strip())
            # print("表格内容:", tables)
            ret_text += non_table_text.strip()
            if tables:
                ret_text += '\n\n\ntable:'+'\n'.join([str(table) for table in tables])+'\n\n\n'
    return ret_text


def parse_pdf_images(file_path, save_dir):
    
    def calculate_distance(img_rect, text_rect):
        """计算图片和文本块之间的距离"""
        # 计算两个矩形中心点之间的距离
        img_center_x = (img_rect[0] + img_rect[2]) / 2
        img_center_y = (img_rect[1] + img_rect[3]) / 2
        text_center_x = (text_rect[0] + text_rect[2]) / 2
        text_center_y = (text_rect[1] + text_rect[3]) / 2
        
        return ((img_center_x - text_center_x) ** 2 + (img_center_y - text_center_y) ** 2) ** 0.5
    
    def is_below(img_rect, text_rect, max_distance):
        """检查文本块是否在图片下方且距离不超过阈值"""
        # img_rect 和 text_rect 都是 (x0, y0, x1, y1) 格式的tuple
        return (text_rect[1] > img_rect[3] and 
                abs(text_rect[0] - img_rect[0]) < 20 and
                (text_rect[1] - img_rect[3]) < max_distance)
    
    def is_above(img_rect, text_rect, max_distance):
        """检查文本块是否在图片上方且距离不超过阈值"""
        # img_rect 和 text_rect 都是 (x0, y0, x1, y1) 格式的tuple
        return (text_rect[3] < img_rect[1] and 
                abs(text_rect[0] - img_rect[0]) < 20 and
                (img_rect[1] - text_rect[3]) < max_distance)
    
    def is_nearby(img_rect, text_rect, max_distance):
        """检查文本块是否在图片附近（任何方向）"""
        distance = calculate_distance(img_rect, text_rect)
        return distance < max_distance
    
    def clean_filename(text):
        """清理文本，使其适合作为文件名"""
        # 移除特殊字符，保留中文、英文、数字
        import re
        # 移除换行符和多余空格
        text = re.sub(r'\s+', ' ', text.strip())
        # 移除文件名不允许的字符
        text = re.sub(r'[<>:"/\\|?*]', '', text)
        # 限制长度
        if len(text) > 50:
            text = text[:50]
        return text
    
    def is_valid_image(img_rect, img_data):
        """检查是否为有效的图片（排除小色块）"""
        # 检查图片尺寸
        width = img_rect[2] - img_rect[0]
        height = img_rect[3] - img_rect[1]
        
        # 过滤掉太小的图片（可能是色块或装饰元素）
        min_width = 30  # 最小宽度30像素
        min_height = 30  # 最小高度30像素
        
        # 检查图片数据大小
        img_size = len(img_data['image'])
        min_img_size = 1024  # 最小1KB
        
        return (width >= min_width and height >= min_height and img_size >= min_img_size)
    
    doc = fitz.open(file_path)
    for page_num in range(len(doc)):
        page = doc[page_num]
        
        # 提取页面上的所有文本块
        text_blocks = page.get_text("dict")["blocks"]
        text_info = []
        for block in text_blocks:
            if "lines" in block:  # 文本块
                for line in block["lines"]:
                    for span in line["spans"]:
                        text_info.append({
                            'text': span['text'].strip(),
                            'bbox': span['bbox'],  # (x0, y0, x1, y1)
                            'font_size': span['size']
                        })
        
        # 提取图片
        tuple_images = page.get_images(full=True)
        for img_idx, xref in enumerate(tuple_images):
            img = doc.extract_image(xref[0])
            
            # 获取图片在页面上的位置
            img_rect = page.get_image_bbox(xref)
            
            # 检查是否为有效图片
            if not is_valid_image(img_rect, img):
                print(f"Skipping small image/color block: {img_rect}")
                continue
            
            # 寻找图片的caption
            caption = ""
            max_distance = 100  # 增加最大距离阈值
            
            # 收集所有附近的文本块，按距离排序
            nearby_texts = []
            for text_item in text_info:
                if is_nearby(img_rect, text_item['bbox'], max_distance):
                    distance = calculate_distance(img_rect, text_item['bbox'])
                    nearby_texts.append({
                        'text': text_item['text'],
                        'distance': distance,
                        'font_size': text_item['font_size'],
                        'bbox': text_item['bbox']
                    })
            
            # 按距离排序
            nearby_texts.sort(key=lambda x: x['distance'])
            
            # 优先选择caption特征明显的文本
            caption_candidates = []
            for text_item in nearby_texts:
                # 检查是否是caption（小字体或包含特定关键词）
                is_caption_like = (
                    text_item['font_size'] < 12 or 
                    any(keyword in text_item['text'].lower() for keyword in ['图', 'figure', 'fig', '表', 'table', '图表'])
                )
                
                # 检查位置关系
                is_below_img = is_below(img_rect, text_item['bbox'], max_distance)
                is_above_img = is_above(img_rect, text_item['bbox'], max_distance)
                
                caption_candidates.append({
                    'text': text_item['text'],
                    'distance': text_item['distance'],
                    'is_caption_like': is_caption_like,
                    'is_below': is_below_img,
                    'is_above': is_above_img
                })
            
            # 选择最佳caption
            if caption_candidates:
                # 优先选择caption特征明显的文本
                caption_like_candidates = [c for c in caption_candidates if c['is_caption_like']]
                if caption_like_candidates:
                    # 在caption特征明显的文本中，优先选择最近的
                    caption_like_candidates.sort(key=lambda x: x['distance'])
                    caption = caption_like_candidates[0]['text']
                else:
                    # 如果没有明显的caption，选择最近的文本
                    caption = caption_candidates[0]['text']
            
            # 生成文件名
            if caption:
                filename = clean_filename(caption)
                if filename:        
                    # 保存图片
                    imgout = open(f"{save_dir}/{filename}.png", "wb")
                    imgout.write(img['image'])
                    imgout.close()
    
    doc.close()


def parse_text_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        contents = f.read()
    return contents


def parse_office_file(file_path):
    if file_path.endswith('.docx'):
        doc = Document(file_path)
        return '\n\n'.join([para.text for para in doc.paragraphs])
    elif file_path.endswith('.pptx'):
        prs = Presentation(file_path)
        return '\n\n'.join([slide.shapes.title.text for slide in prs.slides])
    else:
        return parse_text_file(file_path)


def parse_local_temp_file(file_path):
    if file_path.endswith('.pdf'):
        return parse_pdf_file(file_path)
    elif file_path.endswith('.docx') \
            or file_path.endswith('.pptx'):
        return parse_office_file(file_path)
    else:
        return parse_text_file(file_path)


async def parse_file(file, ftype) -> List[List[str]]:
    # logger.info(f'parse files: {[f.filename for f in files]}')
    file_name,file_content = '',''
    file_path,file_content_binary = None,None
    # 分别处理file是本地文件、URL文件、base64文件、二进制form-data文件
    if ftype == 'binary':
        # 读取二进制form-data文件
        file_name = file.filename
        file_content_binary = await file.read()
        print('========binary file========',len(file_content_binary),file_name)
    elif ftype == 'url':
        print('========str file========',file[:10],file[-10:])
        # 下载文件
        async with httpx.AsyncClient() as client:
            response = await client.get(file)
            file_content_binary = response.content
    elif ftype == 'base64':
        file_content_binary = base64.b64decode(file)
    else:
        raise HTTPException(status_code=400, 
                            detail=f'try parse file failed')

    if file_content_binary:
        # 保存到临时文件
        file_name = file.filename
        file_ext = os.path.splitext(file_name)[1]
        with tempfile.NamedTemporaryFile(suffix=file_ext, delete=True) as temp_file:
            temp_file.write(file_content_binary)
            temp_file.flush()
            temp_file_path = temp_file.name
            logger.info(f'write to temp file: {temp_file_path}')
            try:
                file_content = parse_local_temp_file(temp_file_path)
            except:
                raise HTTPException(status_code=400,
                                    detail=f'try parse binary failed: {traceback.format_exc()}')
    if file_path:
        try:
            file_content = parse_local_temp_file(file_path)
        except:
            raise HTTPException(status_code=400, 
                                detail=f'try parse saved file failed: {traceback.format_exc()}')

    return [file_name, file_content]



if __name__ == "__main__":
    # import asyncio
    # result = asyncio.run(parse_file('/data/dat/rag/2023textileReport.pdf'))
    # print(result)
    parse_pdf_images('/data/dat/rag/科学结构2021.pdf', '/data/dat/rag/tmp')



