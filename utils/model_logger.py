import json
import logging
import time
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
import openai
from logging.handlers import RotatingFileHandler
import concurrent.futures
import pytz

from config import LOG_MODEL_CALL_KEYS

# 获取当前logger
logger = logging.getLogger(__name__)

class ModelLogger:
    """
    模型调用日志记录器
    用于记录所有模型的调用情况，便于后续统计用量
    """
    
    def __init__(self, 
                 log_file='model_calls.log', 
                 max_bytes=10*1024*1024, 
                 max_messages=3,
                 max_content_length=1000):
        self.log_file = log_file
        self.max_messages = max_messages
        self.max_content_length = max_content_length
        
        # 使用项目根目录
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
        self.log_path = os.path.join(log_dir, log_file)
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置专用的logger用于模型调用日志
        self.model_logger = logging.getLogger(f"{__name__}.model_calls")
        self.model_logger.setLevel(logging.INFO)
        
        if not self.model_logger.handlers:
            handler = RotatingFileHandler(
                filename=self.log_path,
                maxBytes=max_bytes,
                encoding='utf-8'
            )
            
            formatter = logging.Formatter('%(message)s')
            handler.setFormatter(formatter)
            self.model_logger.addHandler(handler)
            
            self.model_logger.propagate = False
    
    def _truncate_content(self, content: Any) -> Any:
        """截断消息内容，避免日志过大"""
        if isinstance(content, str):
            if len(content) > self.max_content_length:
                return content[:self.max_content_length] + "..."
            return content
        
        if isinstance(content, list):
            truncated_content = []
            for item in content:
                if isinstance(item, dict):
                    item_copy = item.copy()
                    
                    # 处理图片URL
                    if item.get('type') == 'image_url' and 'image_url' in item:
                        img_url = item.get('image_url', {}).get('url', '')
                        if img_url.startswith('data:image'):
                            item_copy['image_url'] = {'url': 'data:image/...[truncated]'}
                    
                    # 处理文本内容
                    elif item.get('type') == 'text' and 'text' in item:
                        item_copy['text'] = self._truncate_content(item['text'])
                    
                    truncated_content.append(item_copy)
                else:
                    truncated_content.append(item)
            
            return truncated_content
        
        return content
    
    def _truncate_messages(self, messages: List[Dict]) -> List[Dict]:
        """截断消息列表，只保留前几条消息并截断内容"""
        if not messages:
            return messages
        
        # 只保留前N条消息
        limited_messages = messages[:self.max_messages] if len(messages) > self.max_messages else messages
        
        truncated_messages = []
        for msg in limited_messages:
            msg_copy = msg.copy()
            if 'content' in msg_copy:
                msg_copy['content'] = self._truncate_content(msg_copy['content'])
            truncated_messages.append(msg_copy)
        
        return truncated_messages
    
    def _truncate_dict_strings(self, d, max_length=None):
        """递归截断字典中的所有字符串值"""
        if max_length is None:
            max_length = self.max_content_length
            
        if not isinstance(d, dict):
            if isinstance(d, str) and len(d) > max_length:
                return d[:max_length] + "..."
            return d
        
        result = {}
        for k, v in d.items():
            if isinstance(v, str) and len(v) > max_length:
                result[k] = v[:max_length] + "..."
            elif isinstance(v, dict):
                result[k] = self._truncate_dict_strings(v, max_length)
            elif isinstance(v, list):
                result[k] = [
                    item[:max_length] + "..." if isinstance(item, str) and len(item) > max_length
                    else self._truncate_dict_strings(item, max_length) if isinstance(item, dict)
                    else item
                    for item in v
                ]
            else:
                result[k] = v
        return result
    
    def _safe_response_str(self, response, max_length=None):
        if max_length is None:
            max_length = self.max_content_length
            
        if isinstance(response, str):
            return response[:max_length] if len(response) > max_length else response
        
        if response is None or isinstance(response, (int, float, bool)):
            return str(response)
        
        if isinstance(response, (list, dict)):
            size = len(response)
            if size > 100:
                return f"[{type(response).__name__}({size} items)]"
        try:
            result = repr(response)
            return result[:max_length] if len(result) > max_length else result
        except:
            return f"[{type(response).__name__}]"

    def _extract_response_info(self, response: Any) -> tuple[Optional[Dict], Optional[str]]:
        """提取响应信息"""
        if not response:
            return None, None
        
        try:
            # 1. 提取usage信息
            usage = None
            if isinstance(response, (openai.types.chat.chat_completion.ChatCompletion, openai.types.Completion)):
                response_data = response.model_dump()
                usage = response_data.get('usage', {})
            
            response_str = self._safe_response_str(response)
                
            return usage, response_str
            
        except Exception as e:
            logger.warning(f"Failed to extract response info: {str(e)}")
            return None, None
    
    def _extract_minimal_params(self, args, kwargs, decorator_kwargs, extract_keys=None):
        params = {}
        
        extract_all = False
        if extract_keys == 'all' or (isinstance(extract_keys, list) and 'all' in extract_keys):
            extract_all = True
            key_params = []
        else:
            key_params = extract_keys or LOG_MODEL_CALL_KEYS
        
        if extract_all:
            for key, value in kwargs.items():
                params[key] = value
        else:
            for key in key_params:
                if key in kwargs and key not in params:
                    params[key] = kwargs[key]
        
        if args and len(args) > 0 and isinstance(args[0], dict):
            arg_dict = args[0]
            if extract_all:
                for key, value in arg_dict.items():
                    if key not in params:
                        params[key] = value
            else:
                for key in key_params:
                    if key not in params and key in arg_dict:
                        params[key] = arg_dict[key]
        
        if args and len(args) > 1 and isinstance(args[1], dict):
            arg_dict = args[1]
            if extract_all:
                for key, value in arg_dict.items():
                    if key not in params:
                        params[key] = value
            else:
                for key in key_params:
                    if key not in params and key in arg_dict:
                        params[key] = arg_dict[key]

        # 从data参数中提取model
        if 'data' in kwargs and 'model' not in params:
            if isinstance(kwargs['data'], dict) and 'model' in kwargs['data']:
                params['model'] = kwargs['data'].get('model')
        
        # 处理session_id和sid的别名关系
        if 'sid' in params and 'session_id' not in params:
            params['session_id'] = params['sid']
        
        # 添加装饰器参数
        for key, value in decorator_kwargs.items():
            if key != 'extract_keys':
                params[key] = value
        
        # 处理消息截断
        if 'messages' in params:
            processed_params = params.copy()
            try:
                processed_params['messages'] = self._truncate_messages(processed_params['messages'])
            except Exception as e:
                logger.error(f"Error truncating messages: {str(e)}")
            return processed_params
        
        # 添加调试日志
        logger.debug(f"_extract_minimal_params: extracted_params={params}, decorator_kwargs={decorator_kwargs}")
        
        return params
    
    def log_model_call(self, 
                      key: Optional[str],
                      model_name: Optional[str], 
                      url: Optional[str], 
                      response: Any = None, 
                      error: Optional[Exception] = None, 
                      elapsed_time: Optional[int] = None, 
                      completion_params: Optional[Dict] = None,
                      session_id: Optional[str] = None):
        """记录模型调用日志"""
        
        # 强制使用东八区时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        timestamp = datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]    
        response_usage, response_content = self._extract_response_info(response) if not error else (None, None)
        
        logger.info(f"log_model_call: key={key}, model_name={model_name}, url={url}, session_id={session_id}")
        # 创建日志记录
        log_entry = {
            'timestamp': timestamp,
            'session_id': session_id,
            'log_key': key,
            'elapsed_ms': elapsed_time,
            'model': model_name,
            'url': url,
            'status': 'error' if error else 'success',
            'error': str(error) if error else None,
            'completion_params': completion_params,
            'response_usage': response_usage,
            'response_content': response_content
        }
        
        # 使用logger写入，自动处理轮转
        try:
            log_line = json.dumps(log_entry, ensure_ascii=False)
            self.model_logger.info(log_line)
        except Exception as e:
            logger.error(f"Failed to write model call log: {str(e)}")
        
        return log_entry

model_logger = ModelLogger()
_log_executor = concurrent.futures.ThreadPoolExecutor(max_workers=2)



# 异步日志记录函数
def _async_log_model_call(args, kwargs, decorator_kwargs, response, error, elapsed_time):
    try:
        # 提取关键参数，避免处理整个参数列表
        extract_keys = decorator_kwargs.pop('extract_keys', LOG_MODEL_CALL_KEYS)
        extracted_params = model_logger._extract_minimal_params(args, kwargs, decorator_kwargs, extract_keys)
        
        # 记录日志
        model_logger.log_model_call(
            key=extracted_params.get('key'),
            model_name=extracted_params.get('model'),
            url=extracted_params.get('url'),
            response=response,
            error=error,
            elapsed_time=elapsed_time,
            completion_params=extracted_params,
            session_id=extracted_params.get('session_id')
        )
    except Exception as e:
        # 日志错误不应影响主流程
        logger.error(f"Error in async logging: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

# 异步版本的带有自定义参数的装饰器工厂函数
def log_model_call_async(extract_keys=None, **decorator_kwargs):
    """
    异步装饰器：用于记录异步模型调用并允许传入自定义参数
    """
    if extract_keys:
        decorator_kwargs['extract_keys'] = extract_keys
        
    def decorator(func):
        import functools
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            error = None
            response = None
            
            try:
                response = await func(*args, **kwargs)
                return response
            except Exception as e:
                error = e
                raise
            finally:
                elapsed_time = int((time.time() - start_time) * 1000)
                _log_executor.submit(
                    _async_log_model_call,
                    args, kwargs, decorator_kwargs.copy(),
                    response, error, elapsed_time
                )
                
        return wrapper
    return decorator

# 带有自定义参数的装饰器工厂函数
def log_model_call(extract_keys=None, **decorator_kwargs):
    """
    装饰器工厂函数
    """
    if extract_keys:
        decorator_kwargs['extract_keys'] = extract_keys
        
    def decorator(func):
        import functools
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            error = None
            response = None
            
            try:
                response = func(*args, **kwargs)
                return response
            except Exception as e:
                error = e
                raise
            finally:
                elapsed_time = int((time.time() - start_time) * 1000)
                _log_executor.submit(
                    _async_log_model_call,
                    args, kwargs, decorator_kwargs.copy(),
                    response, error, elapsed_time
                )
                
        return wrapper
    return decorator

