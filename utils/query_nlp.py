import re

# 预定义简单问题的特征规则（可根据实际场景调整）
class DictQueryClassifier:
    def __init__(self):
        # 阈值参数（根据实际数据调优）
        self.max_length_simple_question = 30  # 句子最大字符数（含标点）
        self.max_length_chat_query = 7  # 句子最大字符数（含标点）
        self.max_wh_count = 1  # 最多允许1个简单疑问词

        # 简单疑问词集合（覆盖高频基础疑问词）
        self.simple_wh_words = {"谁", "哪里", "何时", "多久", "多少", "几"}
        # 复杂逻辑词（出现这些词通常指向需要分析/推理的复杂问题）
        self.complex_logic_words = {"分析", "比较", "影响", "原因", "机制", "评价", 
                                "探讨", "论证", "挑战", "趋势", "如果"}
        self.chat_features = {
            'greetings': ['你好', '早上好', '嗨', 'hello', 'hi', 'hallo', '您好', '哈啰', '哈喽'],
            'emoticons': ['呲牙', '抓狂', '憨笑', '强', '合十', '偷笑'],
            'informal_terms': ['小姐姐', '小叮当', '友友们', '亲爱的小伙伴们', '浪哥', '伴哥'],
            'mood_words': ['哈哈哈', '啦', '哟', '哇', '呢', '呗'],
            # 'identity_check': ['你谁', '是谁', '你是', '叫什么名字', '哪款模型'],
            'non_content': ['在吗', '啥东西']
        }
        # self.emoji_pattern = re.compile(r'[\u263a-\U0001f645]')  # 简单emoji范围
        # self.punctuation_pattern = re.compile(r'[!?,，。]{2,}')  # 重复标点

        # 定义时间敏感关键词模式
        self.time_patterns = [
            r'(最新|当前|现阶段|最近|新近|目前|当下|现在|当今|此刻|眼下)',
            r'(今年|当月|本周|今日|今天|现年|昨天|前天|上周|上个月|上星期|上周末|下周|下个月|下星期|下周末)',
            r'(年前|明年|后天|后年|节前|节后|春节|元旦|国庆|中秋|端午|清明|五一|十一|国庆节|元旦节|春节|中秋节|端午节|清明节)',
            r'(何时|什么时候|截止时间|更新时间|啥时候|几时|时限|截止日期)',
            r'(将要|即将|预计)',
            r'(白皮书|报告|研究数据)',
            r'(是否过期|是否达标)',
            r'(更新|发布|曝光).*?(了|吗)'
        ]


    def _non_chinese(self, question: str) -> bool:
        """判断输入问题是否完全不包含中文"""
        return not any('\u4e00' <= c <= '\u9fff' for c in question)


    def is_complex_question(self, question: str) -> bool:
        """判断输入问题是否为复杂问题"""
        # 规则1：句子长度超过30字符（含标点）
        threshold = self.max_length_simple_question*3 if self._non_chinese(question) \
                    else self.max_length_simple_question
        if len(question) > threshold:
            print('===长度complex')
            return True

        # 规则2：包含多个简单疑问词
        wh_matches = [word for word in self.simple_wh_words if word in question]
        if len(wh_matches) > self.max_wh_count:
            print('===疑问词complex')
            return True

        # 规则3：包含复杂逻辑词（如"分析""影响"等）
        if any(logic_word in question for logic_word in self.complex_logic_words):
            print('===逻辑词complex')
            return True

        # 规则4：包含反问句/修辞句
        if "难道" in question and "吗？" in question:
            print('===反问句complex')
            return True

        return False


    def is_chat_query(self, question:str) -> bool:
        """判断输入问题是否为聊天问题"""
        threshold = self.max_length_chat_query*3 if self._non_chinese(question) \
                    else self.max_length_chat_query
        if len(question) > threshold:
            return False

        # 特征1：包含词典中的关键词
        for category in self.chat_features.values():
            for word in category:
                if word in question.lower():
                    return True
        
        # 特征4：纯表情/无意义字符
        if len(question.strip()) < 3 and not any(c.isalnum() for c in question):
            return True
        
        # 特征5：包含网络用语/语气词
        if any(word in question for word in ['沙雕', '爆一下', 'cpu干烧']):
            return True
        
        return False


    def is_time_query(self, query):
        """判断输入问题是否为时间问题"""
        # 合并所有匹配规则
        all_patterns = self.time_patterns
        
        # 转换为正则表达式对象
        regex_patterns = [re.compile(p) for p in all_patterns]

        # 检查匹配
        matches = []
        for pattern in regex_patterns:
            match = pattern.findall(query)
            if match:
                matches.extend(match)
        
        # 如果找到任何匹配则返回True
        return len(matches) > 0

        return False



if __name__ == "__main__":
    # 测试用例 simple
    test_complex = [
        # 简单问题
        "棉条安全吗？",
        "今天是多少号",
        "硅胶锅铲安全吗",
        "PVC材质的儿童拖鞋含有对人体有害的物质吗？",
        "塑料杯能装100°的热水吗？",
        "有哪些好用的不粘锅",
        "隐形眼镜安全吗",
        "外卖餐盒有害吗",
        "蔬菜怎么去农残",
        "pla吸管",

        # 复杂问题
        "我正在申请青少年心理健康项目经费，需要3项证明'校园霸凌造成的心理影响'的权威研究数据，请用通俗语言总结数据结论并注明来源。",
        "针对案主张某（曾有自残史的青少年）的戒毒康复计划，生成风险预案设计，过滤高风险建议（如‘电击疗法’），并注意伦理审查事项（如‘需取得监护人知情同意’）",
        "请问下男女性交进去就射了，射完又插了几下，总共不到2分钟，但是拔出来的时候避孕套留在对方阴道里，请问下需要吃阻断药吗，我这算是高危吗",
        "除了304和316不锈钢，还有哪些材质的保温杯在使用过程中是安全环保的？",
        "如何在生活中避免使用到含有有毒有害化学物质的清洁剂？"
    ]

    # 测试示例 chat
    test_chat = [
        "你好啊小姐姐", 
        "星觉醒的美女员工有哪些 呲牙",
        "货拉拉拉不拉拉布拉多？",
        "棉条安全吗？",
        "今天是多少号"
    ]

    # 测试示例 time
    test_time = [
        "志愿者报名截止时间",    # 应命中
        "今天是多少号",         # 应命中 
        "最新的DAC价格是多少",  # 应命中
        "如何避免有毒物质",     # 不应命中
        "特朗普什么时候被刺杀" # 应命中
    ]

    classifier = DictQueryClassifier()
    for q in test_chat:
        result = "Yes" if classifier.is_chat_query(q) else "No"
        print(f"问题：{q}\n判断结果：{result}\n---")


