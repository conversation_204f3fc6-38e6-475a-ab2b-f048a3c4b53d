#encoding=utf8
import asyncio
import logging
import time
import uuid
from collections import defaultdict
from dataclasses import dataclass
from typing import Dict, Optional, Tuple, List, Set

import redis.asyncio as redis

from ssv_logger import ssv_logger
ssv_logger('load_balancer.log')
logger = logging.getLogger(__name__)

from config import MULTI_ENV_MODELS, LOAD_BALANCE_CONFIG, DEFAULT_CONCURRENCY_LIMIT, REDIS_CONFIG


@dataclass
class QueuedRequest:
    """排队的请求"""
    request_id: str
    model: str
    timestamp: float
    future: asyncio.Future
    timeout: float






class RedisConnectionManager:
    """Redis连接管理器，负责连接池和健康检查"""
    
    def __init__(self, redis_url: str, config: Dict):
        self.redis_url = redis_url
        self.config = config
        self._client: Optional[redis.Redis] = None
        self._connection_lock = asyncio.Lock()
        self._last_health_check = 0
        self._health_check_interval = config.get('health_check_interval', 30)
    
    async def get_client(self) -> redis.Redis:
        """获取Redis客户端，带连接池和健康检查"""
        async with self._connection_lock:
            current_time = time.time()
            
            # 如果没有客户端或需要健康检查
            if (self._client is None or 
                current_time - self._last_health_check > self._health_check_interval):
                
                try:
                    if self._client is None:
                        # 创建新连接
                        clean_config = {k: v for k, v in self.config.items() if v is not None}
                        self._client = redis.from_url(self.redis_url, **clean_config)
                    
                    # 健康检查
                    await asyncio.wait_for(self._client.ping(), timeout=2.0)
                    self._last_health_check = current_time
                    logger.debug("Redis connection health check passed")
                    
                except Exception as e:
                    logger.error(f"Redis connection failed: {e}")
                    if self._client:
                        try:
                            await self._client.close()
                        except:
                            pass
                        self._client = None
                    raise ConnectionError(f"Failed to connect to Redis: {e}")
            
            return self._client
    
    async def close(self):
        """关闭Redis连接"""
        if self._client:
            try:
                await self._client.close()
                logger.info("Redis connection closed")
            except Exception as e:
                logger.error(f"Error closing Redis connection: {e}")
            finally:
                self._client = None


class RedisRateLimiter:
    """基于Redis的分布式限流器"""
    
    def __init__(self, redis_url: str = None, key_prefix: str = None):
        # Redis配置
        self.redis_url = redis_url or REDIS_CONFIG.get('url', 'redis://localhost:6379')
        self.key_prefix = key_prefix or REDIS_CONFIG.get('key_prefix', 'load_balancer')
        
        # Redis连接配置
        redis_config = {
            'decode_responses': True,
            'socket_timeout': REDIS_CONFIG.get('socket_timeout', 5),
            'socket_connect_timeout': REDIS_CONFIG.get('connect_timeout', 5),
            'retry_on_timeout': True,
            'health_check_interval': REDIS_CONFIG.get('health_check_interval', 30),
            'max_connections': REDIS_CONFIG.get('max_connections', 10),
            'password': REDIS_CONFIG.get('password')
        }
        
        # Redis连接管理
        self.redis_manager = RedisConnectionManager(self.redis_url, redis_config)
        
        # 健康状态管理（本地缓存）
        self.env_health: Dict[str, bool] = {}
        self.last_health_check: Dict[str, float] = {}
        
        # 后台任务管理
        self.background_tasks: Set[asyncio.Task] = set()
        
        # 请求队列管理
        self.request_queues: Dict[str, List[QueuedRequest]] = defaultdict(list)
        self.queue_lock = asyncio.Lock()
        
        self._initialize_health_status()
    
    async def _get_redis_client(self) -> redis.Redis:
        """获取Redis客户端连接"""
        return await self.redis_manager.get_client()
    
    def _initialize_health_status(self):
        """初始化健康状态（本地缓存）"""
        for model_name, envs in MULTI_ENV_MODELS.items():
            for env_name, config in envs.items():
                semaphore_key = self._get_semaphore_key(model_name, env_name)
                self.env_health[semaphore_key] = True
                self.last_health_check[semaphore_key] = time.time()
                logger.info(f"Initialized health status for {semaphore_key}")
    
    def _get_redis_key(self, model: str, env_type:str) -> str:
        """生成Redis键"""
        return f"{self.key_prefix}:{model}:{env_type}:count"
    
    def _get_health_key(self, model: str, env_type:str) -> str:
        """生成健康状态Redis键"""
        return f"{self.key_prefix}:{model}:{env_type}:health"
    
    def _get_semaphore_key(self, model: str, env_type:str) -> str:
        """生成信号量键"""
        return f"{model}:{env_type}"
    
    def _get_available_endpoints(self, model: str) -> List[Tuple[str, Dict]]:
        """获取模型的可用端点，按优先级排序"""
        if model not in MULTI_ENV_MODELS:
            return []
        
        endpoints = []
        # 获取模型配置，按优先级排序
        for env_name, config in MULTI_ENV_MODELS[model].items():
            semaphore_key = self._get_semaphore_key(model, env_name)
            
            # 检查健康状态
            # if self.env_health.get(semaphore_key, True):
            #     endpoints.append((env_name, config))
            endpoints.append((env_name, config))

        endpoints.sort(key=lambda x: x[1].get('priority', 999))
        return endpoints
    
    async def _try_redis_permit(self, model: str, env_type: str, timeout: float = 0.1) -> bool:
        """尝试获取许可证，支持超时"""
        redis_client = await self._get_redis_client()
        redis_key = self._get_redis_key(model, env_type)
        
        # 从 MULTI_ENV_MODELS 获取并发限制
        limit = DEFAULT_CONCURRENCY_LIMIT
        if model in MULTI_ENV_MODELS and env_type in MULTI_ENV_MODELS[model]:
            limit = MULTI_ENV_MODELS[model][env_type].get('concurrency_limit', DEFAULT_CONCURRENCY_LIMIT)
        
        try:
            # 使用Lua脚本确保原子性
            lua_script = """
            local key = KEYS[1]
            local limit = tonumber(ARGV[1])
            local current = redis.call('GET', key)
            if current == false then
                current = 0
            else
                current = tonumber(current)
            end
            
            if current < limit then
                redis.call('INCR', key)
                redis.call('EXPIRE', key, %d)  -- 配置的过期时间，防止死锁
                return 1
            else
                return 0
            end
            """
            
            # 使用配置的过期时间
            expire_seconds = LOAD_BALANCE_CONFIG.get('permit_expire_seconds', 300)
            formatted_script = lua_script % expire_seconds
            result = await redis_client.eval(formatted_script, 1, redis_key, limit)
            if result == 1:
                logger.debug(f"Acquired permit for {model}:{env_type}, current usage updated in Redis")
                return True
            else:
                logger.debug(f"Failed to acquire permit for {model}:{env_type}, limit reached")
                return False
                
        except Exception as e:
            logger.error(f"Redis error when acquiring permit for {model}:{env_type}: {e}")
            return False
    
    async def _release_usage_permit(self, model: str, env_type: str):
        """释放许可证"""
        redis_client = await self._get_redis_client()
        redis_key = self._get_redis_key(model, env_type)
        
        try:
            # 使用Lua脚本确保原子性
            lua_script = """
            local key = KEYS[1]
            local current = redis.call('GET', key)
            if current ~= false and tonumber(current) > 0 then
                redis.call('DECR', key)
                return 1
            else
                return 0
            end
            """
            
            result = await redis_client.eval(lua_script, 1, redis_key)
            if result == 1:
                logger.debug(f"Released permit for {model}:{env_type}")
            else:
                logger.warning(f"Attempted to release permit for {model}:{env_type} but count was already 0")
                
        except Exception as e:
            logger.error(f"Redis error when releasing permit for {model}:{env_type}: {e}")
    
    async def get_best_endpoint(self, model: str, queue_wait_timeout: float = 10) -> Optional[Tuple[str, Dict]]:
        """获取最佳可用端点，支持等待"""
        if not LOAD_BALANCE_CONFIG.get('enable_auto_fallback', True):
            return None
        
        available_endpoints = self._get_available_endpoints(model)
        if not available_endpoints:
            logger.warning(f"No available endpoints for {model}")
            return None
        
        # 获取用量可用的端点
        endpoint = await self._try_get_usage_available_endpoint(model, available_endpoints)
        if endpoint:
            logger.info(f"No available acquired endpoint {model}:{endpoint[0]}")
            return endpoint
        
        # 如果没有用量可用的端点则进入队列等待
        if queue_wait_timeout > 0:
            logger.info(f"No immediate endpoint available for {model}, entering queue with timeout {queue_wait_timeout}s")
            return await self._wait_for_endpoint(model, queue_wait_timeout)
        
        logger.warning(f"All endpoints for model {model} are busy or unhealthy")
        return None
    
    async def _try_get_usage_available_endpoint(self, model: str, available_endpoints: List[Tuple[str, Dict]]) -> Optional[Tuple[str, Dict]]:
        """尝试Redis可用端点"""
        for env_type, config in available_endpoints:
            try:
                if await self._try_redis_permit(model, env_type, timeout=0.1):
                    logger.debug(f"Acquired endpoint {model}:{env_type}")
                    return (env_type, config)
            except Exception as e:
                logger.warning(f"Failed to acquire permit for {model}:{env_type}: {e}")
                # 标记端点为不健康
                self.mark_endpoint_unhealthy(model, env_type)
                continue
        return None
    
    async def _wait_for_endpoint(self, model: str, timeout: float) -> Optional[Tuple[str, Dict]]:
        """等待可用端点"""
        # 检查队列长度限制
        max_queue_size = LOAD_BALANCE_CONFIG.get('max_queue_size', 100)
        if len(self.request_queues[model]) >= max_queue_size:
            logger.warning(f"Queue for model {model} is full ({max_queue_size}), rejecting request")
            return None
        
        request_id = str(uuid.uuid4())
        future = asyncio.Future()
        
        queued_request = QueuedRequest(
            request_id=request_id,
            model=model,
            timestamp=time.time(),
            future=future,
            timeout=timeout
        )
        
        # 将请求加入队列
        async with self.queue_lock:
            self.request_queues[model].append(queued_request)
            queue_size = len(self.request_queues[model])
            logger.info(f"Request {request_id} queued for model {model}, queue size: {queue_size}")
        
        try:
            # 等待结果或超时
            result = await asyncio.wait_for(future, timeout=timeout)
            logger.info(f"Request {request_id} got endpoint after waiting")
            return result
        except asyncio.TimeoutError:
            logger.warning(f"Request {request_id} timed out after {timeout}s")
            return None
        except Exception as e:
            logger.error(f"Error waiting for endpoint for request {request_id}: {e}")
            return None
        finally:
            # 超时的请求会在下次_cleanup_and_get_valid_requests中被清理
            # 确保Future被正确清理
            if not future.done():
                future.cancel()

    def _cleanup_and_get_valid_requests(self, model: str) -> List[QueuedRequest]:
        """清理过期的请求并返回有效的请求列表"""
        current_time = time.time()
        valid_requests = []
        expired_count = 0
        
        for request in self.request_queues[model]:
            if current_time - request.timestamp <= request.timeout:
                valid_requests.append(request)
            else:
                expired_count += 1
                if not request.future.done():
                    request.future.set_result(None)
        
        # 清空原队列
        self.request_queues[model].clear()
        
        if expired_count > 0:
            logger.info(f"Cleaned up {expired_count} expired requests for model {model}")
        
        return valid_requests
    
    def mark_endpoint_unhealthy(self, model: str, env_type: str):
        """标记端点为不健康（本地缓存）"""
        semaphore_key = self._get_semaphore_key(model, env_type)
        self.env_health[semaphore_key] = False
        logger.warning(f"Marked endpoint {semaphore_key} as unhealthy")
        
        # 异步更新Redis中的健康状态
        self._create_background_task(self._update_health_in_redis(model, env_type, False))
    
    async def _update_health_in_redis(self, model: str, env_type: str, healthy: bool):
        """更新Redis中的健康状态"""
        try:
            redis_client = await self._get_redis_client()
            health_key = self._get_health_key(model, env_type)
            await redis_client.set(health_key, "1" if healthy else "0", ex=3600)  # 1小时过期
        except Exception as e:
            logger.error(f"Failed to update health status in Redis for {model}:{env_type}: {e}")
    
    def release_endpoint(self, model: str, env_type: str):
        """释放端点并处理队列中的等待请求"""
        self._create_background_task(self._release_permit_and_queue(model, env_type))
    
    async def _release_permit_and_queue(self, model: str, env_type: str):
        """释放许可证并处理队列中的等待请求"""
        # 先释放许可证
        await self._release_usage_permit(model, env_type)
        
        # 处理队列中的等待请求
        await self._release_waiting_requests(model)
    
    async def _release_waiting_requests(self, model: str):
        """处理等待队列中的请求"""
        async with self.queue_lock:
            if model not in self.request_queues or not self.request_queues[model]:
                return
            
            # 清理过期的请求并获取有效队列
            waiting_requests = self._cleanup_and_get_valid_requests(model)
        
        if not waiting_requests:
            return
        
        logger.info(f"Processing {len(waiting_requests)} waiting requests for model {model}")
        
        # 尝试为等待的请求分配端点
        available_endpoints = self._get_available_endpoints(model)
        processed_count = 0
        
        for request in waiting_requests:
            # Future已完成则跳过
            if request.future.done():
                logger.debug(f"Request {request.request_id} already completed, skipping")
                continue
            
            # 尝试获取usage可用的端点
            try:
                result = await self._try_get_usage_available_endpoint(model, available_endpoints)
                if result:
                    logger.info(f"Assigned endpoint to waiting request {request.request_id}")
                    if not request.future.done():
                        request.future.set_result(result)
                    processed_count += 1
                else:
                    # 如果还是无法获取端点，重新加入队列
                    async with self.queue_lock:
                        self.request_queues[model].append(request)
            except Exception as e:
                logger.error(f"Error processing waiting request {request.request_id}: {e}")
                if not request.future.done():
                    request.future.set_result(None)
        
        if processed_count > 0:
            logger.info(f"Successfully processed {processed_count} waiting requests for model {model}")
    
    def reset_endpoint_health(self, model: str, env_type: str) -> bool:
        """重置端点健康状态"""
        semaphore_key = self._get_semaphore_key(model, env_type)
        if semaphore_key in self.env_health:
            self.env_health[semaphore_key] = True
            self.last_health_check[semaphore_key] = time.time()
            logger.info(f"Reset health status for {semaphore_key}")
            
            # 异步更新Redis
            self._create_background_task(self._update_health_in_redis(model, env_type, True))
            return True
        return False
    
    async def close(self):
        """关闭Redis连接和清理后台任务"""
        # 取消所有后台任务
        if self.background_tasks:
            logger.info(f"Cancelling {len(self.background_tasks)} background tasks")
            for task in self.background_tasks:
                if not task.done():
                    task.cancel()
            
            # 等待所有任务完成或取消
            if self.background_tasks:
                await asyncio.gather(*self.background_tasks, return_exceptions=True)
            self.background_tasks.clear()
        
        # 关闭Redis连接
        if self.redis_manager:
            await self.redis_manager.close()
            logger.info("Redis connection closed")

    def _create_background_task(self, coro) -> asyncio.Task:
        """创建并跟踪后台任务"""
        task = asyncio.create_task(coro)
        self.background_tasks.add(task)
        
        # 添加回调来清理完成的任务
        def cleanup_task(finished_task):
            try:
                self.background_tasks.discard(finished_task)
                if finished_task.exception():
                    logger.error(f"Background task failed: {finished_task.exception()}")
            except Exception as e:
                logger.error(f"Error in task cleanup: {e}")
        
        task.add_done_callback(cleanup_task)
        return task

    def reset_all_endpoints_health(self):
        """重置所有端点的健康状态"""
        logger.info("Resetting health status for all endpoints")
        
        # 遍历所有模型和环境
        for model_name, envs in MULTI_ENV_MODELS.items():
            for env_name in envs.keys():
                self.reset_endpoint_health(model_name, env_name)
        return True
    
    async def start_daily_health_reset(self, hour: int = None, minute: int = None):
        """启动每日健康状态重置定时任务
        
        Args:
            hour (int): 每天执行的小时（24小时制），默认从配置读取
            minute (int): 每天执行的分钟，默认从配置读取
        """
        # 从配置获取默认值
        hour = hour if hour is not None else LOAD_BALANCE_CONFIG.get('health_reset_hour', 3)
        minute = minute if minute is not None else LOAD_BALANCE_CONFIG.get('health_reset_minute', 0)
        
        logger.info(f"Starting daily health reset scheduler at {hour:02d}:{minute:02d}")
        
        while True:
            try:
                # 计算下一次执行时间
                now = time.time()
                current_time = time.localtime(now)
                
                # 计算今天的目标时间
                target_time_today = time.mktime((
                    current_time.tm_year, 
                    current_time.tm_mon, 
                    current_time.tm_mday,
                    hour, minute, 0, 
                    current_time.tm_wday, 
                    current_time.tm_yday, 
                    current_time.tm_isdst
                ))
                
                # 如果今天的目标时间已经过去，计算明天的目标时间
                if target_time_today <= now:
                    target_time_today += 86400  # 加一天的秒数
                    
                # 计算需要等待的秒数
                wait_seconds = target_time_today - now
                
                logger.info(f"Next health reset scheduled in {wait_seconds:.1f} seconds")
                
                # 等待到指定时间
                await asyncio.sleep(wait_seconds)
                
                # 执行重置
                try:
                    self.reset_all_endpoints_health()
                    logger.info("Daily health reset completed successfully")
                except Exception as e:
                    logger.error(f"Error during daily health reset: {e}")
                
                # 等待一分钟，避免重复执行
                await asyncio.sleep(60)
                
            except asyncio.CancelledError:
                logger.info("Daily health reset scheduler was cancelled")
                break
            except Exception as e:
                logger.error(f"Unexpected error in health reset scheduler: {e}")
                # 等待5分钟后重试
                await asyncio.sleep(300)

LoadBalancedRateLimiter = RedisRateLimiter

# 全局负载均衡限流器实例
load_balancer = LoadBalancedRateLimiter()

# 启动每日健康状态重置定时任务
def start_health_reset_scheduler():
    asyncio.create_task(load_balancer.start_daily_health_reset())

logger.info("Load balancer initialized with multi-environment support") 