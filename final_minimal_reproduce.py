#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
enhanced_recursive_split 函数无限循环的最小复现例子
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.text_processing import enhanced_recursive_split

print("=" * 50)
print("enhanced_recursive_split 无限循环复现")
print("=" * 50)
print()

print("问题位置：utils/text_processing.py 第129-131行")
print("问题代码：")
print("    while start > 0 and chunk[start-1] == chunk[end-1]:")
print("        start -= 1")
print("        end -= 1")
print()

print("最小复现用例：")
print("=" * 30)

# 最小复现用例1：6个字符
print("用例1：6个相同字符")
print("代码：enhanced_recursive_split('aaaaaa', chunk_size=4, chunk_overlap=2)")
print("执行中... (会无限循环，需要手动中断)")
print()

try:
    result = enhanced_recursive_split(
        text="aaaaaa",      # 6个相同字符
        chunk_size=4,       # 块大小4
        chunk_overlap=2     # 重叠2个字符
    )
    print("结果:", result)
except KeyboardInterrupt:
    print("被用户中断")
except Exception as e:
    print("错误:", e)

print()
print("=" * 30)
print("用例2：更短的4个字符")
print("代码：enhanced_recursive_split('aaaa', chunk_size=3, chunk_overlap=1)")
print("执行中...")

try:
    result = enhanced_recursive_split(
        text="aaaa",        # 4个相同字符
        chunk_size=3,       # 块大小3
        chunk_overlap=1     # 重叠1个字符
    )
    print("结果:", result)
except KeyboardInterrupt:
    print("被用户中断")
except Exception as e:
    print("错误:", e)
